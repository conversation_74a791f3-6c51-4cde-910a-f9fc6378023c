@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using JangLegal.DTO

<!-- Plaintiffs Section -->
<div class="card mb-3">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>Plaintiffs</h5>
            <SfButton type="button" OnClick="OpenPlaintiffForm" CssClass="e-primary">Add Plaintiff</SfButton>
        </div>
        <ValidationMessage For="@(() => CaseDto.Plaintiffs)"/>

        @if (CaseDto.Plaintiffs.Any())
        {
            <SfGrid DataSource="CaseDto.Plaintiffs" AllowPaging="true" AllowFiltering="true" AllowSorting="true" AllowTextWrap="true">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn Field="@nameof(PlaintiffDto.PlaintiffType)" HeaderText="Type" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Plaintiff" Width="250px">
                        <Template Context="gridContext">
                            @{
                                if (gridContext is PlaintiffDto plaintiff)
                                {
                                    @if (plaintiff.PlaintiffTypeId == 1)
                                    {
                                        @plaintiff.EmployeeName
                                    }
                                    else if (plaintiff.PlaintiffTypeId == 2)
                                    {
                                        @plaintiff.CouncilOfGroupCompanyName
                                    }
                                    else if (plaintiff.PlaintiffTypeId == 3)
                                    {
                                        @plaintiff.CouncilOfNonGroupCompanyName
                                    }
                                    else if (plaintiff.PlaintiffTypeId == 4)
                                    {
                                        @plaintiff.OtherPlaintiff
                                    }
                                }
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Field="@nameof(PlaintiffDto.LawFirmName)" HeaderText="Law Firm" Width="250px"></GridColumn>
                    <GridColumn Field="@nameof(PlaintiffDto.LawyerName)" HeaderText="Lawyer" Width="250px"></GridColumn>
                    <GridColumn HeaderText="Actions" AutoFit="true">
                        <Template Context="gridContext">
                            @{
                                if (gridContext is PlaintiffDto plaintiff)
                                {
                                    <SfButton OnClick="@(() => EditPlaintiff(plaintiff))"
                                              type="button"
                                              CssClass="e-primary e-small">
                                        Edit
                                    </SfButton>
                                    <SfButton OnClick="@(() => RemovePlaintiff(plaintiff.Id))"
                                              type="button"
                                              CssClass="e-danger e-small">
                                        Remove
                                    </SfButton>
                                }
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

<!-- Plaintiff Form Dialog -->
<SfDialog @bind-Visible="_isPlaintiffDialogOpen" Width="600px" IsModal="true">
    <DialogTemplates>
        <Header>Plaintiff Detail</Header>
        <Content>
            <EditForm Model="_selectedPlaintiff" OnValidSubmit="SavePlaintiff">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="mb-3">
                    <SfDropDownList TItem="PlaintiffTypeDto" TValue="int"
                                    @bind-Value="_selectedPlaintiff.PlaintiffTypeId"
                                    DataSource="@PlaintiffTypes"
                                    Placeholder="Select Plaintiff Type"
                                    FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true"
                                    FilterType="FilterType.Contains">
                        <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="int" TItem="PlaintiffTypeDto"
                                            ValueChange="@(e => OnPlaintiffTypeChanged(e))">
                        </DropDownListEvents>
                    </SfDropDownList>
                </div>

                @if (_selectedPlaintiff.PlaintiffTypeId == 1)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="CaseEmployeeDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.CaseEmployeeId"
                                        DataSource="@Employees"
                                        Placeholder="Select Employee"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Text="Name" Value="Id"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="CaseEmployeeDto"
                                                ValueChange="@(e => OnEmployeeSelectionChanged(e))">
                            </DropDownListEvents>
                        </SfDropDownList>
                    </div>

                    <div class="mb-3">
                        <SfTextBox @bind-Value="_selectedPlaintiff.EmployeeName"
                                   Placeholder="Employee Name"
                                   Enabled="false"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>

                    <div class="mb-3">
                        <SfTextBox @bind-Value="_selectedPlaintiff.EmployeeCode"
                                   Placeholder="Employee Code"
                                   Enabled="false"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId == 2)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="CouncilOfGroupCompanyDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.CouncilOfGroupCompanyId"
                                        DataSource="@GroupCompanyCouncils"
                                        Placeholder="Select Group Company Council"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId == 3)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="CouncilOfNonGroupCompanyDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.CouncilOfNonGroupCompanyId"
                                        DataSource="@NonGroupCompanyCouncils"
                                        Placeholder="Select Non-Group Company Council"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId == 4)
                {
                    <div class="mb-3">
                        <SfTextBox @bind-Value="_selectedPlaintiff.OtherPlaintiff"
                                   Placeholder="Other Plaintiff"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                }

                @if (_selectedPlaintiff.PlaintiffTypeId != 4)
                {
                    <div class="mb-3">
                        <SfDropDownList TItem="LawFirmDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.LawFirmId"
                                        DataSource="@LawFirms"
                                        Placeholder="Select Law Firm"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                            <DropDownListEvents TValue="int?" TItem="LawFirmDto"
                                                ValueChange="@(e => OnLawFirmChanged(e))">
                            </DropDownListEvents>
                        </SfDropDownList>
                    </div>

                    <div class="mb-3">
                        <SfDropDownList TItem="LawyerDto" TValue="int?"
                                        @bind-Value="_selectedPlaintiff.LawyerId"
                                        DataSource="@FilteredPlaintiffLawyers"
                                        Placeholder="Select Lawyer"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        Enabled="@(_selectedPlaintiff.LawFirmId.HasValue)"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Text="Name" Value="Id"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                }

                <div class="d-flex gap-2 justify-content-end">
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                    <SfButton OnClick="@(() => _isPlaintiffDialogOpen = false)" type="button"
                              CssClass="e-outline">
                        Cancel
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [Parameter] public CourtCaseDto CaseDto { get; set; } = new();
    [Parameter] public int? CaseId { get; set; }
    [Parameter] public List<PlaintiffTypeDto> PlaintiffTypes { get; set; } = new();
    [Parameter] public List<CaseEmployeeDto> Employees { get; set; } = new();
    [Parameter] public List<CouncilOfGroupCompanyDto> GroupCompanyCouncils { get; set; } = new();
    [Parameter] public List<CouncilOfNonGroupCompanyDto> NonGroupCompanyCouncils { get; set; } = new();
    [Parameter] public List<LawFirmDto> LawFirms { get; set; } = new();
    [Parameter] public List<LawyerDto> Lawyers { get; set; } = new();
    [Parameter] public EventCallback OnDataChanged { get; set; }

    private bool _isPlaintiffDialogOpen;
    private PlaintiffDto _selectedPlaintiff = new();

    private List<LawyerDto> FilteredPlaintiffLawyers => _selectedPlaintiff.LawFirmId.HasValue
        ? Lawyers.Where(l => l.LawFirmId == _selectedPlaintiff.LawFirmId).ToList()
        : new List<LawyerDto>();

    private void OpenPlaintiffForm()
    {
        _selectedPlaintiff = new PlaintiffDto
        {
            CourtCaseId = CaseId ?? 0,
            Id = Guid.NewGuid()
        };
        _isPlaintiffDialogOpen = true;
    }

    private void EditPlaintiff(PlaintiffDto plaintiff)
    {
        _selectedPlaintiff = new PlaintiffDto
        {
            Id = plaintiff.Id,
            CourtCaseId = plaintiff.CourtCaseId,
            PlaintiffTypeId = plaintiff.PlaintiffTypeId,
            PlaintiffType = plaintiff.PlaintiffType,
            CaseEmployeeId = plaintiff.CaseEmployeeId,
            EmployeeCode = plaintiff.EmployeeCode,
            EmployeeName = plaintiff.EmployeeName,
            CNIC = plaintiff.CNIC,
            Department = plaintiff.Department,
            Designation = plaintiff.Designation,
            CouncilOfGroupCompanyId = plaintiff.CouncilOfGroupCompanyId,
            CouncilOfGroupCompanyName = plaintiff.CouncilOfGroupCompanyName,
            CouncilOfNonGroupCompanyId = plaintiff.CouncilOfNonGroupCompanyId,
            CouncilOfNonGroupCompanyName = plaintiff.CouncilOfNonGroupCompanyName,
            OtherPlaintiff = plaintiff.OtherPlaintiff,
            LawFirmId = plaintiff.LawFirmId,
            LawFirmName = plaintiff.LawFirmName,
            LawyerId = plaintiff.LawyerId,
            LawyerName = plaintiff.LawyerName
        };
        _isPlaintiffDialogOpen = true;
    }

    private void SavePlaintiff()
    {
        // Set plaintiff type name
        var plaintiffType = PlaintiffTypes.FirstOrDefault(t => t.Id == _selectedPlaintiff.PlaintiffTypeId);
        if (plaintiffType != null)
        {
            _selectedPlaintiff.PlaintiffType = plaintiffType.Title;
        }

        // Set council names if applicable
        if (_selectedPlaintiff.CouncilOfGroupCompanyId.HasValue)
        {
            var groupCouncil = GroupCompanyCouncils.FirstOrDefault(c => c.Id == _selectedPlaintiff.CouncilOfGroupCompanyId);
            _selectedPlaintiff.CouncilOfGroupCompanyName = groupCouncil?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.CouncilOfGroupCompanyName = "";
        }

        if (_selectedPlaintiff.CouncilOfNonGroupCompanyId.HasValue)
        {
            var nonGroupCouncil = NonGroupCompanyCouncils.FirstOrDefault(c => c.Id == _selectedPlaintiff.CouncilOfNonGroupCompanyId);
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = nonGroupCouncil?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = "";
        }

        // Set law firm and lawyer names if applicable
        if (_selectedPlaintiff.LawFirmId.HasValue)
        {
            var lawFirm = LawFirms.FirstOrDefault(f => f.Id == _selectedPlaintiff.LawFirmId);
            _selectedPlaintiff.LawFirmName = lawFirm?.Title ?? "";
        }
        else
        {
            _selectedPlaintiff.LawFirmName = "";
        }

        if (_selectedPlaintiff.LawyerId.HasValue)
        {
            var lawyer = Lawyers.FirstOrDefault(l => l.Id == _selectedPlaintiff.LawyerId);
            _selectedPlaintiff.LawyerName = lawyer?.Name ?? "";
        }
        else
        {
            _selectedPlaintiff.LawyerName = "";
        }

        var existingPlaintiff = CaseDto.Plaintiffs.FirstOrDefault(p => p.Id == _selectedPlaintiff.Id);
        if (existingPlaintiff != null)
        {
            // Update existing plaintiff
            existingPlaintiff.PlaintiffTypeId = _selectedPlaintiff.PlaintiffTypeId;
            existingPlaintiff.PlaintiffType = _selectedPlaintiff.PlaintiffType;
            existingPlaintiff.CaseEmployeeId = _selectedPlaintiff.CaseEmployeeId;
            existingPlaintiff.EmployeeName = _selectedPlaintiff.EmployeeName;
            existingPlaintiff.EmployeeCode = _selectedPlaintiff.EmployeeCode;
            existingPlaintiff.CNIC = _selectedPlaintiff.CNIC;
            existingPlaintiff.Department = _selectedPlaintiff.Department;
            existingPlaintiff.Designation = _selectedPlaintiff.Designation;
            existingPlaintiff.CouncilOfGroupCompanyId = _selectedPlaintiff.CouncilOfGroupCompanyId;
            existingPlaintiff.CouncilOfGroupCompanyName = _selectedPlaintiff.CouncilOfGroupCompanyName;
            existingPlaintiff.CouncilOfNonGroupCompanyId = _selectedPlaintiff.CouncilOfNonGroupCompanyId;
            existingPlaintiff.CouncilOfNonGroupCompanyName = _selectedPlaintiff.CouncilOfNonGroupCompanyName;
            existingPlaintiff.OtherPlaintiff = _selectedPlaintiff.OtherPlaintiff;
            existingPlaintiff.LawFirmId = _selectedPlaintiff.LawFirmId;
            existingPlaintiff.LawFirmName = _selectedPlaintiff.LawFirmName;
            existingPlaintiff.LawyerId = _selectedPlaintiff.LawyerId;
            existingPlaintiff.LawyerName = _selectedPlaintiff.LawyerName;
        }
        else
        {
            // Add new plaintiff
            CaseDto.Plaintiffs.Add(_selectedPlaintiff);
        }

        // Sort plaintiffs by name or other appropriate field
        CaseDto.Plaintiffs = CaseDto.Plaintiffs.OrderBy(c =>
            c.PlaintiffTypeId == 1 ? c.EmployeeName :
            c.PlaintiffTypeId == 2 ? c.CouncilOfGroupCompanyName :
            c.PlaintiffTypeId == 3 ? c.CouncilOfNonGroupCompanyName :
            c.OtherPlaintiff).ToList();

        _isPlaintiffDialogOpen = false;
        OnDataChanged.InvokeAsync();
    }

    private void RemovePlaintiff(Guid id)
    {
        CaseDto.Plaintiffs = CaseDto.Plaintiffs.Where(c => c.Id != id).ToList();
        OnDataChanged.InvokeAsync();
    }

    private void OnPlaintiffTypeChanged(ChangeEventArgs<int, PlaintiffTypeDto> args)
    {
        _selectedPlaintiff.PlaintiffTypeId = args.Value;
        var selectedType = PlaintiffTypes.FirstOrDefault(t => t.Id == args.Value);
        if (selectedType != null)
        {
            _selectedPlaintiff.PlaintiffType = selectedType.Title;
        }

        // Reset fields based on plaintiff type
        if (_selectedPlaintiff.PlaintiffTypeId != 1)
        {
            _selectedPlaintiff.CaseEmployeeId = null;
            _selectedPlaintiff.EmployeeName = string.Empty;
            _selectedPlaintiff.EmployeeCode = string.Empty;
            _selectedPlaintiff.CNIC = string.Empty;
            _selectedPlaintiff.Department = string.Empty;
            _selectedPlaintiff.Designation = string.Empty;
        }

        if (_selectedPlaintiff.PlaintiffTypeId != 2)
        {
            _selectedPlaintiff.CouncilOfGroupCompanyId = null;
            _selectedPlaintiff.CouncilOfGroupCompanyName = string.Empty;
        }

        if (_selectedPlaintiff.PlaintiffTypeId != 3)
        {
            _selectedPlaintiff.CouncilOfNonGroupCompanyId = null;
            _selectedPlaintiff.CouncilOfNonGroupCompanyName = string.Empty;
        }

        if (_selectedPlaintiff.PlaintiffTypeId != 4)
        {
            _selectedPlaintiff.OtherPlaintiff = string.Empty;
        }

        StateHasChanged();
    }

    private void OnLawFirmChanged(ChangeEventArgs<int?, LawFirmDto> args)
    {
        _selectedPlaintiff.LawFirmId = args.Value;
        _selectedPlaintiff.LawyerId = null;

        if (args.Value.HasValue)
        {
            var lawFirm = LawFirms.FirstOrDefault(f => f.Id == args.Value);
            if (lawFirm != null)
            {
                _selectedPlaintiff.LawFirmName = lawFirm.Title;
            }
        }
        else
        {
            _selectedPlaintiff.LawFirmName = string.Empty;
        }

        StateHasChanged();
    }

    private void OnEmployeeSelectionChanged(ChangeEventArgs<int?, CaseEmployeeDto> args)
    {
        if (args.Value.HasValue)
        {
            var selectedEmployee = Employees.FirstOrDefault(e => e.Id == args.Value);
            if (selectedEmployee != null)
            {
                _selectedPlaintiff.EmployeeName = selectedEmployee.Name;
                _selectedPlaintiff.EmployeeCode = selectedEmployee.EmployeeCode;
                _selectedPlaintiff.CNIC = selectedEmployee.CNIC;
                _selectedPlaintiff.Department = selectedEmployee.Department;
                _selectedPlaintiff.Designation = selectedEmployee.Designation;
            }
        }
        else
        {
            _selectedPlaintiff.EmployeeName = string.Empty;
            _selectedPlaintiff.EmployeeCode = string.Empty;
            _selectedPlaintiff.CNIC = string.Empty;
            _selectedPlaintiff.Department = string.Empty;
            _selectedPlaintiff.Designation = string.Empty;
        }

        StateHasChanged();
    }
}
