@page "/court-cases/new"
@page "/court-cases/edit/{Id:int}"
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using JangLegal.Components.CourtCase
@rendermode InteractiveServer
@inject CourtCaseService CaseService
@inject PlaintiffTypeService PlaintiffTypeService
@inject NavigationManager NavMgr
@inject LawFirmDataService LawFirmService
@inject CourtDataService CourtService
@inject CaseVerdictService VerdictService
@inject CaseCategoryService CategoryService
@inject CaseEmployeeService EmployeeService
@inject CaseNatureService CaseNatureService
@inject AttachmentService AttachmentService
@inject CaseProceedingDataService ProceedingService
@inject IJSRuntime JSRuntime
<style>
    .e-accordion .e-acrdn-item.e-select > .e-acrdn-header{
        background-color: #e0e0e0
    }
    .e-accordion .e-acrdn-item .e-acrdn-header .e-acrdn-header-content {
        width: calc(100% - 30px) !important;
    }
    .e-accordion .e-acrdn-item.e-select.e-selected.e-expand-state > .e-acrdn-header:hover, .e-accordion .e-acrdn-item.e-select.e-expand-state > .e-acrdn-header:hover {
        background-color: #e0e0e0
    }
</style>
<div class="d-flex justify-content-between align-items-center mb-3">
    <h3>@(Id == null ? "New Court Case" : "Edit Court Case")</h3>
    <SfButton OnClick="@(() => NavMgr.NavigateTo("/court-cases", true))" CssClass="e-outline">Back to List</SfButton>
</div>
<SfToast @ref="ToastObj"/>
<SfDialog ShowCloseIcon="true" CloseOnEscape="true" Width="95vw" Height="95vw" IsModal="true" @ref="dlgPreview" Visible="false">
    <DialogTemplates>
        <Header>Case Document Preview</Header>
        <Content>
            @if (!string.IsNullOrEmpty(_documentLink))
            {
                <embed src="@_documentLink" type="@GetMimeType(_documentLink)" width="100%" height="100%" style="object-fit:contain" />
            }
            else
            {
                <p>No document available for preview.</p>
            }
        </Content>
    </DialogTemplates>
</SfDialog>

<EditForm Model="CaseDto" OnValidSubmit="SaveCase" FormName="court_case_form">
<DataAnnotationsValidator/>
<ValidationSummary/>

<!-- Basic Case Information Component -->
<CaseBasicInfo CaseDto="CaseDto"
               Courts="Courts"
               Categories="Categories"
               CaseNatures="CaseNatures" />

<!-- Case Pray Component -->
<CasePray CaseDto="CaseDto" />

<!-- Case Synopsis Component -->
<CaseSynopsis CaseDto="CaseDto" />

<!-- Plaintiff Management Component -->
<PlaintiffManagement CaseDto="CaseDto"
                     CaseId="Id"
                     PlaintiffTypes="_plaintiffTypes"
                     Employees="_employees"
                     GroupCompanyCouncils="_groupCompanyCouncils"
                     NonGroupCompanyCouncils="_nonGroupCompanyCouncils"
                     LawFirms="_lawFirms"
                     Lawyers="_lawyers"
                     OnDataChanged="StateHasChanged" />

<!-- Respondent Management Component -->
<RespondentManagement CaseDto="CaseDto"
                      LawFirms="LawFirms"
                      Lawyers="Lawyers"
                      OnDataChanged="StateHasChanged" />

<!-- Attachment Management Component -->
<AttachmentManagement CaseDto="CaseDto"
                      CaseId="Id"
                      AttachmentTypes="_attachmentTypes"
                      OnShowToast="ShowToastMessage"
                      OnDataChanged="StateHasChanged" />

<div class="d-flex gap-2 justify-content-end mb-3">
    <SfButton Type="submit" CssClass="e-primary">Save Case</SfButton>
    <SfButton OnClick="@(() => NavMgr.NavigateTo("/court-cases", true))" type="button"
              CssClass="e-outline">
        Back to Cases
    </SfButton>
</div>

<!-- Case Proceedings Section - Only visible when editing -->
@if (Id.HasValue)
{
    <div class="card mb-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>Case Proceedings</h5>
                <SfButton type="button" OnClick="OpenProceedingDialog" CssClass="e-primary">Add Proceeding</SfButton>
            </div>

            @if (CaseDto.CaseProceedings.Any())
            {
                <div class="proceedings-timeline">
                    <SfAccordion>
                        <AccordionItems>
                            @foreach (var proceeding in CaseDto.CaseProceedings.OrderBy(p => p.ProceedingDate))
                            {
                                <AccordionItem>
                                    <HeaderTemplate>
                                        <div class="proceeding-accordion-header-enhanced">
                                            <div class="proceeding-main-info">
                                                <div class="proceeding-date-badge">
                                                    <i class="material-icons">event</i>
                                                    <span class="date-text">@proceeding.ProceedingDateFormatted</span>
                                                </div>
                                                <div class="proceeding-meta">
                                                    <span class="proceeding-type-badge">@proceeding.ProceedingTypeName</span>
                                                    <span class="proceeding-status-indicator">@proceeding.ProceedingStatusName</span>
                                                </div>
                                            </div>
                                            <div class="proceeding-quick-info">
                                                @if (!string.IsNullOrEmpty(proceeding.PresidingJudge))
                                                {
                                                    <div class="quick-info-item">
                                                        <i class="material-icons">gavel</i>
                                                        <span>@proceeding.PresidingJudge</span>
                                                    </div>
                                                }
                                                @if (!string.IsNullOrEmpty(proceeding.CourtRoomNumber))
                                                {
                                                    <div class="quick-info-item">
                                                        <i class="material-icons">meeting_room</i>
                                                        <span>Room @proceeding.CourtRoomNumber</span>
                                                    </div>
                                                }
                                            </div>
                                            <div class="proceeding-actions-enhanced">
                                                <SfButton OnClick="@(() => EditProceeding(proceeding))"
                                                          type="button"
                                                          CssClass="e-info e-small action-btn">
                                                    <i class="material-icons">edit</i>
                                                </SfButton>
                                                <SfButton OnClick="@(() => DeleteProceeding(proceeding.ProceedingId))"
                                                          type="button"
                                                          CssClass="e-danger e-small action-btn">
                                                    <i class="material-icons">delete</i>
                                                </SfButton>
                                            </div>
                                        </div>
                                    </HeaderTemplate>
                                    <ContentTemplate>
                                        <div class="proceeding-details-enhanced">
                                            <!-- Key Information Cards -->
                                            <div class="proceeding-info-grid">
                                                <div class="info-card primary-info">
                                                    <div class="info-header">
                                                        <i class="material-icons">gavel</i>
                                                        <span class="info-title">Proceeding Details</span>
                                                    </div>
                                                    <div class="info-content">
                                                        <div class="info-row">
                                                            <span class="info-label">Type</span>
                                                            <span class="info-value highlight">@proceeding.ProceedingTypeName</span>
                                                        </div>
                                                        <div class="info-row">
                                                            <span class="info-label">Status</span>
                                                            <span class="info-value status-badge">@proceeding.ProceedingStatusName</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="info-card court-info">
                                                    <div class="info-header">
                                                        <i class="material-icons">account_balance</i>
                                                        <span class="info-title">Court Information</span>
                                                    </div>
                                                    <div class="info-content">
                                                        <div class="info-row">
                                                            <span class="info-label">Presiding Judge</span>
                                                            <span class="info-value">@proceeding.PresidingJudge</span>
                                                        </div>
                                                        <div class="info-row">
                                                            <span class="info-label">Court Room</span>
                                                            <span class="info-value">@proceeding.CourtRoomNumber</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                @if (proceeding.NextProceedingDate.HasValue)
                                                {
                                                    <div class="info-card schedule-info">
                                                        <div class="info-header">
                                                            <i class="material-icons">schedule</i>
                                                            <span class="info-title">Next Proceeding</span>
                                                        </div>
                                                        <div class="info-content">
                                                            <div class="next-date-display">
                                                                <span class="next-date">@proceeding.NextProceedingDateFormatted</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>

                                            @if (!string.IsNullOrEmpty(proceeding.Remarks))
                                            {
                                                <div class="remarks-section">
                                                    <div class="remarks-header">
                                                        <i class="material-icons">note</i>
                                                        <span class="remarks-title">Remarks</span>
                                                    </div>
                                                    <div class="remarks-content">@proceeding.Remarks</div>
                                                </div>
                                            }

                                            <!-- Proceeding Summary -->
                                            <div class="outcome-management-enhanced">
                                                <div class="management-header">
                                                    <div class="header-content">
                                                        <i class="material-icons">assignment_turned_in</i>
                                                        <h4 class="section-title">Outcomes</h4>
                                                    </div>
                                                    <SfButton OnClick="@(() => AddNewOutcome(proceeding.ProceedingId))" type="button" CssClass="e-primary e-small">
                                                        <i class="material-icons">add</i> Add Outcome
                                                    </SfButton>
                                                </div>

                                                <!-- Outcomes List -->
                                                @if (proceeding.ProceedingOutcomes.Any())
                                                {
                                                    <div class="outcomes-list">
                                                        @foreach (var outcome in proceeding.ProceedingOutcomes)
                                                        {
                                                            <div class="outcome-item card mb-2">
                                                                <div class="card-body">
                                                                    <div class="d-flex justify-content-between align-items-start">
                                                                        <div class="outcome-details flex-grow-1">
                                                                            <div class="row">
                                                                                <div class="col-md-8">
                                                                                    <strong>@outcome.OutcomeTypeName</strong>
                                                                                    <p class="mb-1">@outcome.OutcomeDescription</p>
                                                                                    @if (!string.IsNullOrEmpty(outcome.OrderIssuedBy))
                                                                                    {
                                                                                        <small class="text-muted">Order by: @outcome.OrderIssuedBy</small>
                                                                                    }
                                                                                </div>
                                                                                <div class="col-md-4 text-end">
                                                                                    @if (outcome.OrderDate.HasValue)
                                                                                    {
                                                                                        <small class="text-muted">@outcome.OrderDateFormatted</small>
                                                                                    }
                                                                                    <div class="mt-2">
                                                                                        <SfButton OnClick="@(() => EditOutcome(outcome))" type="button" CssClass="e-info e-small me-1">Edit</SfButton>
                                                                                        <SfButton OnClick="@(() => DeleteOutcome(outcome.OutcomeId))" type="button" CssClass="e-danger e-small">Delete</SfButton>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        }
                                                    </div>
                                                }
                                                else
                                                {
                                                    <p class="text-muted">No outcomes recorded yet.</p>
                                                }
                                            </div>

                                            <div class="attendee-management-enhanced">
                                                <div class="management-header">
                                                    <div class="header-content">
                                                        <i class="material-icons">people</i>
                                                        <h4 class="section-title">Attendees</h4>
                                                    </div>
                                                    <SfButton OnClick="@(() => AddNewAttendee(proceeding.ProceedingId))" type="button" CssClass="e-primary e-small">
                                                        <i class="material-icons">person_add</i> Add Attendee
                                                    </SfButton>
                                                </div>

                                                <!-- Attendees List -->
                                                @if (proceeding.ProceedingAttendees.Any())
                                                {
                                                    <div class="attendees-list">
                                                        @foreach (var attendee in proceeding.ProceedingAttendees)
                                                        {
                                                            <div class="attendee-item card mb-2">
                                                                <div class="card-body">
                                                                    <div class="d-flex justify-content-between align-items-center">
                                                                        <div class="attendee-details">
                                                                            <strong>@attendee.PersonName</strong>
                                                                            <span class="badge @(attendee.IsPresent ? "bg-success" : "bg-warning") ms-2">
                                                                                @attendee.IsPresentText
                                                                            </span>
                                                                            @if (!string.IsNullOrEmpty(attendee.RoleInProceedingName))
                                                                            {
                                                                                <div class="text-muted small">Role: @attendee.RoleInProceedingName</div>
                                                                            }
                                                                        </div>
                                                                        <div>
                                                                            <SfButton OnClick="@(() => EditAttendee(attendee))" type="button" CssClass="e-info e-small me-1">Edit</SfButton>
                                                                            <SfButton OnClick="@(() => DeleteAttendee(attendee.AttendeeId))" type="button" CssClass="e-danger e-small">Delete</SfButton>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        }
                                                    </div>
                                                }
                                                else
                                                {
                                                    <p class="text-muted">No attendees recorded yet.</p>
                                                }
                                            </div>

                                            <div class="document-management-enhanced">
                                                <div class="management-header">
                                                    <div class="header-content">
                                                        <i class="material-icons">description</i>
                                                        <h4 class="section-title">Documents</h4>
                                                    </div>
                                                    <SfButton OnClick="@(() => AddNewDocument(proceeding.ProceedingId))" type="button" CssClass="e-primary e-small">
                                                        <i class="material-icons">attach_file</i> Add Document
                                                    </SfButton>
                                                </div>

                                                <!-- Documents List -->
                                                @if (proceeding.ProceedingDocuments.Any())
                                                {
                                                    <div class="documents-list">
                                                        @foreach (var document in proceeding.ProceedingDocuments.OrderByDescending(d => d.SubmissionDate))
                                                        {
                                                            <div class="document-item card mb-2">
                                                                <div class="card-body">
                                                                    <div class="row align-items-center">
                                                                        <div class="col-md-8">
                                                                            <div class="d-flex align-items-center">
                                                                                <i class="material-icons me-2 text-primary">description</i>
                                                                                <div>
                                                                                    <strong>@document.DocumentTitle</strong>
                                                                                    <div class="text-muted small">
                                                                                        Type: @document.DocumentType |
                                                                                        Submitted: @document.SubmissionDateFormatted
                                                                                        @if (!string.IsNullOrEmpty(document.SubmittedBy))
                                                                                        {
                                                                                            <span> by @document.SubmittedBy</span>
                                                                                        }
                                                                                    </div>
                                                                                    @if (!string.IsNullOrEmpty(document.Remarks))
                                                                                    {
                                                                                        <div class="text-muted small">@document.Remarks</div>
                                                                                    }
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-4 text-end">
                                                                            <div class="btn-group">
                                                                                <SfButton OnClick="@(() => DownloadDocument(document.DocumentId))"
                                                                                          type="button" CssClass="e-success e-small">
                                                                                    <i class="material-icons">download</i>
                                                                                </SfButton>
                                                                                <SfButton OnClick="@(() => ShowDocumentPreview(document.FileUrl))"
                                                                                          type="button" CssClass="e-danger e-small">
                                                                                    <i class="material-icons">preview</i>
                                                                                </SfButton>
                                                                                <SfButton OnClick="@(() => DeleteDocument(document.DocumentId))"
                                                                                          type="button" CssClass="e-danger e-small">
                                                                                    <i class="material-icons">delete</i>
                                                                                </SfButton>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        }
                                                    </div>
                                                }
                                                else
                                                {
                                                    <p class="text-muted">No documents uploaded yet.</p>
                                                }
                                            </div>
                                        </div>
                                    </ContentTemplate>
                                </AccordionItem>
                            }
                        </AccordionItems>
                    </SfAccordion>
                </div>
            }
            else
            {
                <p class="text-muted">No proceedings recorded yet.</p>
            }
        </div>
    </div>
}


</EditForm>


<!-- Verdict Selector Dialog -->
<SfDialog @bind-Visible="_isVerdictSelectorOpen" Width="800px" IsModal="true">
    <DialogTemplates>
        <Header>Select Verdict</Header>
        <Content>
            <SfGrid DataSource="Verdicts" AllowPaging="true">
                <GridColumns>
                    <GridColumn Field="@nameof(CaseVerdictDto.Title)" HeaderText="Title"></GridColumn>
                    <GridColumn Field="@nameof(CaseVerdictDto.Description)" HeaderText="Description"></GridColumn>
                    <GridColumn HeaderText="Actions" Width="100">
                        <Template>
                            @{
                                if (context is CaseVerdictDto verdict)
                                {
                                    <SfButton OnClick="@(() => SelectVerdict(verdict))"
                                              type="button"
                                              CssClass="e-primary">
                                        Select
                                    </SfButton>
                                }
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </Content>
    </DialogTemplates>
</SfDialog>









<!-- Proceeding Form Dialog -->
<SfDialog @bind-Visible="_isProceedingDialogOpen" Width="800px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>@(_selectedProceeding.ProceedingId == 0 ? "Add Proceeding" : "Edit Proceeding")</Header>
        <Content>
            <EditForm Model="_selectedProceeding" OnValidSubmit="SaveProceeding">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDatePicker TValue="DateTime" @bind-Value="_selectedProceeding.ProceedingDate"
                                      Placeholder="Proceeding Date"
                                      FloatLabelType="FloatLabelType.Always">
                        </SfDatePicker>
                        <ValidationMessage For="@(() => _selectedProceeding.ProceedingDate)"/>
                    </div>

                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TItem="ProceedingTypeDto" TValue="int?"
                                        @bind-Value="_selectedProceeding.ProceedingTypeId"
                                        DataSource="@_proceedingTypes"
                                        Placeholder="Select Proceeding Type"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Text="TypeName" Value="ProceedingTypeId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TItem="ProceedingStatusDto" TValue="int?"
                                        @bind-Value="_selectedProceeding.ProceedingStatusId"
                                        DataSource="@_proceedingStatuses"
                                        Placeholder="Select Proceeding Status"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Text="StatusName" Value="ProceedingStatusId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfTextBox @bind-Value="_selectedProceeding.PresidingJudge"
                                   Placeholder="Presiding Judge"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfTextBox @bind-Value="_selectedProceeding.CourtRoomNumber"
                                   Placeholder="Court Room Number"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDatePicker TValue="DateTime?" @bind-Value="_selectedProceeding.NextProceedingDate"
                                      Placeholder="Next Proceeding Date"
                                      FloatLabelType="FloatLabelType.Always">
                        </SfDatePicker>
                    </div>
                </div>

                <div class="mb-3">
                    <SfTextBox @bind-Value="_selectedProceeding.Remarks"
                               Placeholder="Remarks"
                               Multiline="true"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => _isProceedingDialogOpen = false)" type="button"
                              CssClass="e-outline-secondary">
                        Cancel
                    </SfButton>
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Outcome Management Dialog -->
<SfDialog @bind-Visible="_isOutcomeDialogOpen" Width="900px" Height="600px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>Manage Proceeding Outcomes</Header>
        <Content>

        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Outcome Form Dialog -->
<SfDialog @bind-Visible="_isOutcomeFormDialogOpen" Width="600px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>@(_selectedOutcome.OutcomeId == 0 ? "Add Outcome" : "Edit Outcome")</Header>
        <Content>
            <EditForm Model="_selectedOutcome" OnValidSubmit="SaveOutcome">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="mb-3">
                    <SfDropDownList TItem="OutcomeTypeDto" TValue="int?"
                                    @bind-Value="_selectedOutcome.OutcomeTypeId"
                                    DataSource="@_outcomeTypes"
                                    Placeholder="Select Outcome Type"
                                    FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true"
                                    FilterType="FilterType.Contains">
                        <DropDownListFieldSettings Text="TypeName" Value="OutcomeTypeId"></DropDownListFieldSettings>
                    </SfDropDownList>
                </div>

                <div class="mb-3">
                    <SfTextBox @bind-Value="_selectedOutcome.OutcomeDescription"
                               Placeholder="Outcome Description"
                               Multiline="true"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                    <ValidationMessage For="@(() => _selectedOutcome.OutcomeDescription)"/>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfTextBox @bind-Value="_selectedOutcome.OrderIssuedBy"
                                   Placeholder="Order Issued By"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfDatePicker TValue="DateOnly?" @bind-Value="_selectedOutcome.OrderDate"
                                      Placeholder="Order Date"
                                      FloatLabelType="FloatLabelType.Always">
                        </SfDatePicker>
                    </div>
                </div>

                <div class="mb-3">
                    <SfTextBox @bind-Value="_selectedOutcome.DocumentReference"
                               Placeholder="Document Reference"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => _isOutcomeFormDialogOpen = false)" type="button"
                              CssClass="e-outline-secondary">
                        Cancel
                    </SfButton>
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Attendee Management Dialog -->
<SfDialog @bind-Visible="_isAttendeeDialogOpen" Width="900px" Height="600px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>Manage Proceeding Attendees</Header>
        <Content>

        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Attendee Form Dialog -->
<SfDialog @bind-Visible="_isAttendeeFormDialogOpen" Width="500px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>@(_selectedAttendee.AttendeeId == 0 ? "Add Attendee" : "Edit Attendee")</Header>
        <Content>
            <EditForm Model="_selectedAttendee" OnValidSubmit="SaveAttendee">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="mb-3">
                    <SfTextBox @bind-Value="_selectedAttendee.PersonName"
                               Placeholder="Person Name"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                    <ValidationMessage For="@(() => _selectedAttendee.PersonName)"/>
                </div>

                <div class="mb-3">
                    <SfDropDownList TItem="RoleInProceedingDto" TValue="int?"
                                    @bind-Value="_selectedAttendee.RoleInProceedingId"
                                    DataSource="@_roleInProceedings"
                                    Placeholder="Select Role"
                                    FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true"
                                    FilterType="FilterType.Contains">
                        <DropDownListFieldSettings Text="RoleName" Value="RoleInProceedingId"></DropDownListFieldSettings>
                    </SfDropDownList>
                </div>

                <div class="mb-3">
                    <SfCheckBox @bind-Checked="_selectedAttendee.IsPresent" Label="Is Present"></SfCheckBox>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => _isAttendeeFormDialogOpen = false)" type="button"
                              CssClass="e-outline-secondary">
                        Cancel
                    </SfButton>
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Document Management Dialog -->
<SfDialog @bind-Visible="_isDocumentDialogOpen" Width="1000px" Height="700px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>Manage Proceeding Documents</Header>
        <Content>

        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Document Form Dialog -->
<SfDialog @bind-Visible="_isDocumentFormDialogOpen" Width="600px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>Add Document</Header>
        <Content>
            <EditForm Model="_selectedDocument" OnValidSubmit="SaveDocument">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="mb-3">
                    <SfTextBox @bind-Value="_selectedDocument.DocumentType"
                               Placeholder="Document Type"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                    <ValidationMessage For="@(() => _selectedDocument.DocumentType)"/>
                </div>

                <div class="mb-3">
                    <SfTextBox @bind-Value="_selectedDocument.DocumentTitle"
                               Placeholder="Document Title"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                    <ValidationMessage For="@(() => _selectedDocument.DocumentTitle)"/>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDatePicker TValue="DateOnly" @bind-Value="_selectedDocument.SubmissionDate"
                                      Placeholder="Submission Date"
                                      FloatLabelType="FloatLabelType.Always">
                        </SfDatePicker>
                        <ValidationMessage For="@(() => _selectedDocument.SubmissionDate)"/>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfTextBox @bind-Value="_selectedDocument.SubmittedBy"
                                   Placeholder="Submitted By"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                </div>

                <div class="mb-3">
                    <SfTextBox @bind-Value="_selectedDocument.Remarks"
                               Placeholder="Remarks"
                               Multiline="true"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                </div>

                <div class="mb-3">
                    <label class="form-label">Select File</label>
                    <div class="file-upload-container">
                        <InputFile OnChange="OnDocumentFileSelected"
                                   class="file-input"
                                   id="documentFileInput"
                                   accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.bmp"/>
                        <label for="documentFileInput" class="file-upload-label">
                            <i class="material-icons">cloud_upload</i>
                            <span>Choose File</span>
                        </label>
                    </div>

                    @if (!string.IsNullOrEmpty(_documentFileValidationMessage))
                    {
                        <div class="text-danger mt-2">
                            <i class="material-icons">error</i>
                            @_documentFileValidationMessage
                        </div>
                    }

                    @if (_selectedDocumentFile != null)
                    {
                        <div class="file-preview mt-3">
                            <div class="file-info">
                                <i class="material-icons">description</i>
                                <div class="file-details">
                                    <div class="file-name">@_selectedDocumentFile.Name</div>
                                    <div class="file-size">@FormatFileSize(_selectedDocumentFile.Size)</div>
                                </div>
                                <button type="button" class="btn-remove" @onclick="ClearSelectedDocumentFile">
                                    <i class="material-icons">close</i>
                                </button>
                            </div>
                        </div>
                    }

                    <div class="file-requirements mt-2">
                        <small class="text-muted">
                            <i class="material-icons">info</i>
                            Supported formats: PDF, Word, Excel, Images (Max 10MB)
                        </small>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => _isDocumentFormDialogOpen = false)" type="button"
                              CssClass="e-outline-secondary">
                        Cancel
                    </SfButton>
                    <SfButton Type="submit" CssClass="e-primary" Disabled="@(_selectedDocumentFile == null)">Upload</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>
@if (ShowOverlay)
    {
        <div class="overlay">
            <div class="overlay-content">
                <p>Please wait...</p>
                <div class="spinner"></div>
            </div>
        </div>
    }


@code
{
    [Parameter] public int? Id { get; set; }
    private bool ShowOverlay = true;
    private string? _documentLink; // Your variable to hold the document link
    private SfDialog? dlgPreview;

    private List<LawFirmDto> LawFirms { get; set; } = new();
    private SfToast? ToastObj;
    private List<LawyerDto> Lawyers { get; set; } = new();

    private CourtCaseDto CaseDto { get; set; } = new() { Plaintiffs = new List<PlaintiffDto>(), Respondents = new List<RespondentDto>(), CaseProceedings = new List<CaseProceedingDto>() };
    private List<CourtDto> Courts { get; set; } = new();
    private List<CaseCategoryDto> Categories { get; set; } = new();
    private List<CaseNatureDto> CaseNatures { get; set; } = new();
    private List<CaseVerdictDto> Verdicts { get; set; } = new();

    // Data for components
    private List<CaseEmployeeDto> _employees = new();
    private List<CouncilOfGroupCompanyDto> _groupCompanyCouncils = new();
    private List<CouncilOfNonGroupCompanyDto> _nonGroupCompanyCouncils = new();
    private List<PlaintiffTypeDto> _plaintiffTypes = new();
    private List<LawFirmDto> _lawFirms = new();
    private List<LawyerDto> _lawyers = new();
    private List<AttachmentTypeDto> _attachmentTypes = new();

    // Dialog states for remaining functionality
    private bool _isPrayDialogOpen;
    private bool _isSynopsisSelectorOpen;
    private bool _isVerdictSelectorOpen;
    private bool _isProceedingDialogOpen;
    private bool _isOutcomeDialogOpen;
    private bool _isAttendeeDialogOpen;
    private bool _isDocumentDialogOpen;
    private bool _isOutcomeFormDialogOpen;
    private bool _isAttendeeFormDialogOpen;
    private bool _isDocumentFormDialogOpen;

    // Proceeding-related variables
    private CaseProceedingDto _selectedProceeding = new();
    private List<ProceedingTypeDto> _proceedingTypes = new();
    private List<ProceedingStatusDto> _proceedingStatuses = new();
    private List<RoleInProceedingDto> _roleInProceedings = new();
    private List<OutcomeTypeDto> _outcomeTypes = new();

    // Outcome management variables
    private ProceedingOutcomeDto _selectedOutcome = new();
    private int _currentProceedingId;
    private List<ProceedingOutcomeDto> _currentOutcomes = new();

    // Attendee management variables
    private ProceedingAttendeeDto _selectedAttendee = new();
    private List<ProceedingAttendeeDto> _currentAttendees = new();

    // Document management variables
    private ProceedingDocumentDto _selectedDocument = new();
    private List<ProceedingDocumentDto> _currentDocuments = new();
    private InputFileChangeEventArgs? _selectedFileArgs;

    // Document file upload variables
    private IBrowserFile? _selectedDocumentFile;
    private string _documentFileValidationMessage = string.Empty;


    protected override async Task OnParametersSetAsync()
    {
        await LoadReferenceData();
        if (Id.HasValue)
        {
            await LoadCase();
        }
    }

    private async Task LoadReferenceData()
    {
        Courts = await CourtService.GetCourtsAsync();
        Categories = await CategoryService.GetCategoriesAsync();
        CaseNatures = await CaseNatureService.GetCaseNaturesAsync();

        Verdicts = await VerdictService.GetVerdictsAsync();
        _employees = await EmployeeService.GetCaseEmployeesAsync();
        _groupCompanyCouncils = await CaseService.GetGroupCompanyCouncilsAsync();
        _nonGroupCompanyCouncils = await CaseService.GetNonGroupCompanyCouncilsAsync();
        LawFirms = await LawFirmService.GetLawFirmsAsync();
        Lawyers = await LawFirmService.GetLawyersAsync();
        _plaintiffTypes = await PlaintiffTypeService.GetPlaintiffTypesAsync();
        _lawFirms = await LawFirmService.GetLawFirmsAsync();
        _lawyers = await LawFirmService.GetLawyersAsync();
        _attachmentTypes = await AttachmentService.GetAttachmentTypesAsync();

        // Load proceeding-related data
        _proceedingTypes = await ProceedingService.GetProceedingTypesAsync();
        _proceedingStatuses = await ProceedingService.GetProceedingStatusesAsync();
        _roleInProceedings = await ProceedingService.GetRoleInProceedingsAsync();
        _outcomeTypes = await ProceedingService.GetOutcomeTypesAsync();
    }

    private async Task LoadCase()
    {
        ShowOverlay=true;
        if (Id.HasValue)
        {
            var loadedCase = await CaseService.GetCourtCaseByIdAsync(Id.Value);
            if (loadedCase != null)
            {
                CaseDto = loadedCase;
            }
        }
        ShowOverlay=false;
    }

    private async Task SaveCase()
    {
        ShowOverlay = true;
        var result = await CaseService.SaveCourtCaseAsync(CaseDto, "jawaid");
        if (result == "OK")
        {
            // display success message by using toastobj
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = "Case saved successfully",
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        else
        {
            // display error message
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        ShowOverlay = false;
    }

    private async Task ShowToastMessage(string message)
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Info",
            Content = message,
            CssClass = "e-info",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 3000
        });
    }




    #region Synopsis Management

    private void OpenSynopsisSelector()
    {
        _isSynopsisSelectorOpen = true;
    }

    #endregion

    #region Verdict Management

    private void OpenVerdictSelector()
    {
        _isVerdictSelectorOpen = true;
    }

    private void RemoveVerdict()
    {
        CaseDto.CaseVerdictId = null;
        CaseDto.CaseVerdictTitle = "";
    }

    private void SelectVerdict(CaseVerdictDto verdict)
    {
        CaseDto.CaseVerdictId = verdict.Id;
        CaseDto.CaseVerdictTitle = verdict.Title;
        _isVerdictSelectorOpen = false;
    }

    #endregion

    #region Proceeding Management

    private void OpenProceedingDialog()
    {
        _selectedProceeding = new CaseProceedingDto
        {
            CourtCaseId = Id ?? 0,
            ProceedingDate = DateTime.Today
        };
        _isProceedingDialogOpen = true;
    }

    private void EditProceeding(CaseProceedingDto proceeding)
    {
        _selectedProceeding = new CaseProceedingDto
        {
            ProceedingId = proceeding.ProceedingId,
            CourtCaseId = proceeding.CourtCaseId,
            ProceedingDate = proceeding.ProceedingDate,
            PresidingJudge = proceeding.PresidingJudge,
            CourtRoomNumber = proceeding.CourtRoomNumber,
            NextProceedingDate = proceeding.NextProceedingDate,
            Remarks = proceeding.Remarks,
            ProceedingTypeId = proceeding.ProceedingTypeId,
            ProceedingStatusId = proceeding.ProceedingStatusId
        };
        _isProceedingDialogOpen = true;
    }

    private async Task SaveProceeding()
    {
        ShowOverlay = true;
        var result = await ProceedingService.SaveProceedingAsync(_selectedProceeding, "jawaid");
        if (result.Success)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

            // Reload case data to refresh proceedings
            await LoadCase();
            _isProceedingDialogOpen = false;
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        ShowOverlay = false;
    }

    private async Task DeleteProceeding(int proceedingId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this proceeding? This will also delete all associated outcomes, attendees, and documents.");
        if (confirmed)
        {
            var result = await ProceedingService.DeleteProceedingAsync(proceedingId, "jawaid");
            if (result.Success)
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = result.Message,
                    CssClass = "e-success",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });

                // Reload case data to refresh proceedings
                await LoadCase();
            }
            else
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = result.Message,
                    CssClass = "e-error",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });
            }
        }
    }

    private async Task ManageOutcomes(int proceedingId)
    {
        _currentProceedingId = proceedingId;
        var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == proceedingId);
        _currentOutcomes = proceeding?.ProceedingOutcomes ?? new List<ProceedingOutcomeDto>();
        _isOutcomeDialogOpen = true;
    }

    private async Task ManageAttendees(int proceedingId)
    {
        _currentProceedingId = proceedingId;
        var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == proceedingId);
        _currentAttendees = proceeding?.ProceedingAttendees ?? new List<ProceedingAttendeeDto>();
        _isAttendeeDialogOpen = true;
    }

    private async Task ManageDocuments(int proceedingId)
    {
        _currentProceedingId = proceedingId;
        var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == proceedingId);
        _currentDocuments = proceeding?.ProceedingDocuments ?? new List<ProceedingDocumentDto>();
        _isDocumentDialogOpen = true;
    }

    #region Outcome Management Methods

    private void AddNewOutcome(int proceedingId)
    {
        _selectedOutcome = new ProceedingOutcomeDto
        {
            ProceedingId = proceedingId
        };
        _isOutcomeFormDialogOpen = true;
    }

    private void EditOutcome(ProceedingOutcomeDto outcome)
    {
        _selectedOutcome = new ProceedingOutcomeDto
        {
            OutcomeId = outcome.OutcomeId,
            ProceedingId = outcome.ProceedingId,
            OutcomeDescription = outcome.OutcomeDescription,
            OrderIssuedBy = outcome.OrderIssuedBy,
            OrderDate = outcome.OrderDate,
            DocumentReference = outcome.DocumentReference,
            OutcomeTypeId = outcome.OutcomeTypeId
        };
        _isOutcomeFormDialogOpen = true;
    }

    private async Task SaveOutcome()
    {
        ShowOverlay = true;
        var result = await ProceedingService.SaveOutcomeAsync(_selectedOutcome, "jawaid");
        if (result.Success)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

            // Reload case data to refresh outcomes
            await LoadCase();
            _isOutcomeFormDialogOpen = false;

            // Refresh current outcomes list
            var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
            _currentOutcomes = proceeding?.ProceedingOutcomes ?? new List<ProceedingOutcomeDto>();
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        ShowOverlay = false;
    }

    private async Task DeleteOutcome(int outcomeId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this outcome?");
        if (confirmed)
        {
            var result = await ProceedingService.DeleteOutcomeAsync(outcomeId, "jawaid");
            if (result.Success)
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = result.Message,
                    CssClass = "e-success",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });

                // Reload case data to refresh outcomes
                await LoadCase();

                // Refresh current outcomes list
                var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
                _currentOutcomes = proceeding?.ProceedingOutcomes ?? new List<ProceedingOutcomeDto>();
            }
            else
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = result.Message,
                    CssClass = "e-error",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });
            }
        }
    }

    #endregion

    #region Attendee Management Methods

    private void AddNewAttendee(int proceedingId)
    {
        _selectedAttendee = new ProceedingAttendeeDto
        {
            ProceedingId = proceedingId,
            IsPresent = true
        };
        _isAttendeeFormDialogOpen = true;
    }

    private void EditAttendee(ProceedingAttendeeDto attendee)
    {
        _selectedAttendee = new ProceedingAttendeeDto
        {
            AttendeeId = attendee.AttendeeId,
            ProceedingId = attendee.ProceedingId,
            PersonName = attendee.PersonName,
            IsPresent = attendee.IsPresent,
            RoleInProceedingId = attendee.RoleInProceedingId
        };
        _isAttendeeFormDialogOpen = true;
    }

    private async Task SaveAttendee()
    {
        ShowOverlay = true;
        var result = await ProceedingService.SaveAttendeeAsync(_selectedAttendee, "jawaid");
        if (result.Success)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

            // Reload case data to refresh attendees
            await LoadCase();
            _isAttendeeFormDialogOpen = false;

            // Refresh current attendees list
            var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
            _currentAttendees = proceeding?.ProceedingAttendees ?? new List<ProceedingAttendeeDto>();
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        ShowOverlay = !true;
    }

    private async Task DeleteAttendee(int attendeeId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this attendee?");
        if (confirmed)
        {
            var result = await ProceedingService.DeleteAttendeeAsync(attendeeId, "jawaid");
            if (result.Success)
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = result.Message,
                    CssClass = "e-success",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });

                // Reload case data to refresh attendees
                await LoadCase();

                // Refresh current attendees list
                var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
                _currentAttendees = proceeding?.ProceedingAttendees ?? new List<ProceedingAttendeeDto>();
            }
            else
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = result.Message,
                    CssClass = "e-error",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });
            }
        }
    }

    #endregion

    #region Document Management Methods

    private int _proceedingId = 0;
    private void AddNewDocument(int proceedingId)
    {
        _proceedingId = proceedingId;
        _selectedDocument = new ProceedingDocumentDto
        {
            ProceedingId = proceedingId,
            SubmissionDate = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day)
        };
        _selectedFileArgs = null;
        _selectedDocumentFile = null;
        _documentFileValidationMessage = string.Empty;
        _isDocumentFormDialogOpen = true;
    }

    @* private void OnFileSelected(InputFileChangeEventArgs e)
            {
                _selectedFileArgs = e;
            } *@

private async Task SaveDocument()
{
    ShowOverlay = true;
    if (_selectedDocumentFile == null)
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Error",
            Content = "Please select a file to upload",
            CssClass = "e-error",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 5000
        });
        return;
    }

    var result = await ProceedingService.SaveDocumentAsync(
        _selectedDocumentFile,
        _selectedDocument,
        Id ?? 0 ,
        _proceedingId,
        "jawaid");

    if (result.Success)
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Success",
            Content = result.Message,
            CssClass = "e-success",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 5000
        });

        // Reload case data to refresh documents
        await LoadCase();
        _isDocumentFormDialogOpen = false;

        // Refresh current documents list
        var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
        _currentDocuments = proceeding?.ProceedingDocuments ?? new List<ProceedingDocumentDto>();
    }
    else
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Error",
            Content = result.Message,
            CssClass = "e-error",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 5000
        });
    }
    ShowOverlay = !true;
}

private async Task DeleteDocument(int documentId)
{
    var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this document?");
    if (confirmed)
    {
        var result = await ProceedingService.DeleteDocumentAsync(documentId, "jawaid");
        if (result.Success)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

            // Reload case data to refresh documents
            await LoadCase();

            // Refresh current documents list
            var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
            _currentDocuments = proceeding?.ProceedingDocuments ?? new List<ProceedingDocumentDto>();
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
    }
}


private async Task DownloadDocument(int documentId)
{
    var result = await ProceedingService.DownloadDocumentAsync(documentId);
    if (result is { Success: true, FileData: not null })
    {
        await JSRuntime.InvokeVoidAsync("downloadFile", result.FileName, Convert.ToBase64String(result.FileData));
    }
    else
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Error",
            Content = "Failed to download document",
            CssClass = "e-error",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 5000
        });
    }
}

private string FormatFileSize(long bytes)
{
    if (bytes == 0) return "0 B";

    string[] sizes = new[] { "B", "KB", "MB", "GB" };
    var order = 0;
    double size = bytes;

    while (size >= 1024 && order < sizes.Length - 1)
    {
        order++;
        size /= 1024;
    }

    return $"{size:0.##} {sizes[order]}";
}

#endregion

#endregion

private void OnPlaintiffTypeChanged(ChangeEventArgs<int, PlaintiffTypeDto> args)
{
    _selectedPlaintiff.PlaintiffTypeId = args.Value;
    var selectedType = _plaintiffTypes.FirstOrDefault(t => t.Id == args.Value);
    if (selectedType != null)
    {
        _selectedPlaintiff.PlaintiffType = selectedType.Title;
    }

    // Reset fields based on plaintiff type
    if (_selectedPlaintiff.PlaintiffTypeId != 1)
    {
        _selectedPlaintiff.CaseEmployeeId = null;
        _selectedPlaintiff.EmployeeName = string.Empty;
        _selectedPlaintiff.EmployeeCode = string.Empty;
        _selectedPlaintiff.CNIC = string.Empty;
        _selectedPlaintiff.Department = string.Empty;
        _selectedPlaintiff.Designation = string.Empty;
    }

    if (_selectedPlaintiff.PlaintiffTypeId != 2)
    {
        _selectedPlaintiff.CouncilOfGroupCompanyId = null;
        _selectedPlaintiff.CouncilOfGroupCompanyName = string.Empty;
    }

    if (_selectedPlaintiff.PlaintiffTypeId != 3)
    {
        _selectedPlaintiff.CouncilOfNonGroupCompanyId = null;
        _selectedPlaintiff.CouncilOfNonGroupCompanyName = string.Empty;
    }

    if (_selectedPlaintiff.PlaintiffTypeId != 4)
    {
        _selectedPlaintiff.OtherPlaintiff = string.Empty;
    }

    StateHasChanged();
}

private void OnLawFirmChanged(ChangeEventArgs<int?, LawFirmDto> args)
{
    _selectedPlaintiff.LawFirmId = args.Value;
    _selectedPlaintiff.LawyerId = null;

    if (args.Value.HasValue)
    {
        var lawFirm = _lawFirms.FirstOrDefault(f => f.Id == args.Value);
        if (lawFirm != null)
        {
            _selectedPlaintiff.LawFirmName = lawFirm.Title;
        }
    }
    else
    {
        _selectedPlaintiff.LawFirmName = string.Empty;
    }

    StateHasChanged();
}

private void OnEmployeeSelectionChanged(ChangeEventArgs<int?, CaseEmployeeDto> args)
{
    if (args.Value.HasValue)
    {
        var selectedEmployee = _employees.FirstOrDefault(e => e.Id == args.Value);
        if (selectedEmployee != null)
        {
            _selectedPlaintiff.EmployeeName = selectedEmployee.Name;
            _selectedPlaintiff.EmployeeCode = selectedEmployee.EmployeeCode;
            _selectedPlaintiff.CNIC = selectedEmployee.CNIC;
            _selectedPlaintiff.Department = selectedEmployee.Department;
            _selectedPlaintiff.Designation = selectedEmployee.Designation;
        }
    }
    else
    {
        _selectedPlaintiff.EmployeeName = string.Empty;
        _selectedPlaintiff.EmployeeCode = string.Empty;
        _selectedPlaintiff.CNIC = string.Empty;
        _selectedPlaintiff.Department = string.Empty;
        _selectedPlaintiff.Designation = string.Empty;
    }

    StateHasChanged();
}

//private async Task OnGroupCouncilChange(ChangeEventArgs<int?, CouncilOfGroupCompanyDto> args)
//{
//    if (args.Value.HasValue)
//    {
//        var council = _groupCompanyCouncils.FirstOrDefault(c => c.Id == args.Value);
//        if (council != null)
//        {
//            _selectedPlaintiff.CouncilOfGroupCompanyName = council.Name;
//        }
//    }
//    else
//    {
//        _selectedPlaintiff.CouncilOfGroupCompanyName = string.Empty;
//    }
//}

//private async Task OnNonGroupCouncilChange(ChangeEventArgs<int?, CouncilOfNonGroupCompanyDto> args)
//{
//    if (args.Value.HasValue)
//    {
//        var council = _nonGroupCompanyCouncils.FirstOrDefault(c => c. == args.Value);
//        if (council != null)
//        {
//            _selectedPlaintiff.CouncilOfNonGroupCompanyName = council.Name;
//        }
//    }
//    else
//    {
//        _selectedPlaintiff.CouncilOfNonGroupCompanyName = string.Empty;
//    }
//}


public void EditRespondent(RespondentDto resp)
{
    _selectedRespondent = resp;
    _isRespondentDialogOpen = true;
}

public void RemoveRespondent(RespondentDto resp)
{
    //CaseDto.Respondents?.Remove(resp);
    CaseDto.Respondents = CaseDto.Respondents.Where(c => c.Id != resp.Id).ToList();
}

public void SaveRespondent()
{
    ShowOverlay = true;
    if (_selectedRespondent.LawFirmId.HasValue)
    {
        var lawFirm = LawFirms.First(f => f.Id == _selectedRespondent.LawFirmId);
        _selectedRespondent.LawFirmName = lawFirm.Title;
    }
    else
    {
        _selectedRespondent.LawFirmName = "";
    }

    if (_selectedRespondent.LawyerId.HasValue)
    {
        var lawyer = Lawyers.FirstOrDefault(l => l.Id == _selectedRespondent.LawyerId);
        _selectedRespondent.LawyerName = lawyer == null ? "" : lawyer.Name;
    }
    else
    {
        _selectedRespondent.LawyerName = "";
    }

    var rs = CaseDto.Respondents.FirstOrDefault(c => c.Id == _selectedRespondent.Id);
    if (rs != null)
    {
        rs.LawyerName = _selectedRespondent.LawyerName;
        rs.LawyerId = _selectedRespondent.LawyerId;
        rs.LawFirmId = _selectedRespondent.LawFirmId;
        rs.LawFirmName = _selectedRespondent.LawFirmName;
        rs.Name = _selectedRespondent.Name;
    }
    else
    {
        rs = new RespondentDto
        {
            LawFirmName = _selectedRespondent.LawFirmName,
            LawFirmId = _selectedRespondent.LawFirmId,
            Id = _selectedRespondent.Id,
            LawyerId = _selectedRespondent.LawyerId,
            LawyerName = _selectedRespondent.LawyerName,
            Name = _selectedRespondent.Name
        };

        CaseDto.Respondents.Add(rs);
        CaseDto.Respondents = CaseDto.Respondents.OrderBy(c => c.Name).ToList();
    }

    _isRespondentDialogOpen = false;
    ShowOverlay = !true;
}

public void SaveRespondent2()
{
    // Set the law firm name and lawyer name before saving
    if (_selectedRespondent.LawFirmId.HasValue)
    {
        var lawFirm = LawFirms.FirstOrDefault(f => f.Id == _selectedRespondent.LawFirmId);
        _selectedRespondent.LawFirmName = lawFirm?.Title ?? "";
    }
    else
    {
        _selectedRespondent.LawFirmName = "";
    }

    if (_selectedRespondent.LawyerId.HasValue)
    {
        var lawyer = Lawyers.FirstOrDefault(l => l.Id == _selectedRespondent.LawyerId);
        _selectedRespondent.LawyerName = lawyer?.Name ?? "";
    }
    else
    {
        _selectedRespondent.LawyerName = "";
    }

    if (CaseDto.Respondents == null)
    {
        CaseDto.Respondents = new List<RespondentDto>();
    }

    var existingRespondent = CaseDto.Respondents.FirstOrDefault(r => r.Id == _selectedRespondent.Id);
    if (existingRespondent != null)
    {
        // Update existing respondent
        var index = CaseDto.Respondents.IndexOf(existingRespondent);
        CaseDto.Respondents[index] = _selectedRespondent;
    }
    else
    {
        // Add new respondent
        CaseDto.Respondents.Add(_selectedRespondent);
    }

    _isRespondentDialogOpen = false;
}

//private void onlawfirmchange(int? lawfirmid)
//{
//    _selectedrespondent.lawfirmid = lawfirmid;
//    _selectedrespondent.lawyerid = null; // reset lawyer selection when law firm changes
//    statehaschanged();
//}

#region Attachment Management

private void OpenAttachmentDialog()
{
    _selectedAttachment = new AttachmentDto
    {
        CourtCaseId = Id ?? 0
    };
    _selectedFile = null;
    _fileValidationMessage = string.Empty;
    _customFileName = string.Empty;
    _isAttachmentDialogOpen = true;
}

private void OnFileSelected(InputFileChangeEventArgs e)
{
    _selectedFile = e.File;
    _fileValidationMessage = string.Empty;

    // Validate file
    if (_selectedFile != null)
    {
        var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
        var extension = Path.GetExtension(_selectedFile.Name).ToLowerInvariant();

        if (!allowedExtensions.Contains(extension))
        {
            _fileValidationMessage = $"File type not allowed. Allowed types: {string.Join(", ", allowedExtensions)}";
            _selectedFile = null;
            return;
        }

        if (_selectedFile.Size > 10 * 1024 * 1024) // 10MB
        {
            _fileValidationMessage = "File size cannot exceed 10 MB";
            _selectedFile = null;
        }
    }
}

private void ClearSelectedFile()
{
    _selectedFile = null;
    _fileValidationMessage = string.Empty;
}

private void OnDocumentFileSelected(InputFileChangeEventArgs e)
{
    _selectedDocumentFile = e.File;
    _documentFileValidationMessage = string.Empty;

    // Validate file
    if (_selectedDocumentFile != null)
    {
        var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
        var extension = Path.GetExtension(_selectedDocumentFile.Name).ToLowerInvariant();

        if (!allowedExtensions.Contains(extension))
        {
            _documentFileValidationMessage = $"File type not allowed. Allowed types: {string.Join(", ", allowedExtensions)}";
            _selectedDocumentFile = null;
            return;
        }

        if (_selectedDocumentFile.Size > 10 * 1024 * 1024) // 10MB
        {
            _documentFileValidationMessage = "File size cannot exceed 10 MB";
            _selectedDocumentFile = null;
        }
    }
}


public async Task ShowDocumentPreview(string documentLink)
    {
        _documentLink = documentLink;
        if (dlgPreview != null)
        {
            await dlgPreview.ShowAsync();
        }
    }

    // Helper method to determine the MIME type based on the file extension
    private string GetMimeType(string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return "application/octet-stream"; // Default generic binary type
        }

        string extension = Path.GetExtension(fileName).ToLowerInvariant();

        return extension switch
        {
            ".pdf" => "application/pdf",
            ".jpg" => "image/jpeg",
            ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            // Add more image types as needed
            _ => "application/octet-stream", // Fallback for unknown types
        };
    }


private void ClearSelectedDocumentFile()
{
    _selectedDocumentFile = null;
    _documentFileValidationMessage = string.Empty;
}

private async Task SaveAttachment()
{
    ShowOverlay = true;
    if (_selectedFile == null || _selectedAttachment.AttachmentTypeId == null)
    {
        return;
    }

    try
    {
        var result = await AttachmentService.SaveAttachmentAsync(
            _selectedFile,
            _customFileName,
            Id ?? 0,
            _selectedAttachment.AttachmentTypeId.Value,
            "jawaid");

        if (result.Success)
        {
            // Reload attachments
            if (Id.HasValue && CaseDto != null)
            {
                CaseDto.Attachments = await AttachmentService.GetAttachmentsByCourtCaseIdAsync(Id.Value);
            }

            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

            _isAttachmentDialogOpen = false;
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
    }
    catch (Exception ex)
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Error",
            Content = $"Error uploading file: {ex.Message}",
            CssClass = "e-error",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 5000
        });
    }
    ShowOverlay = !true;
}

private async Task DownloadAttachment(AttachmentDto attachment)
{
    await JSRuntime.InvokeVoidAsync("open", attachment.FileUrl, "_blank");
}

private async Task DeleteAttachment(AttachmentDto attachment)
{
    try
    {
        var result = await AttachmentService.DeleteAttachmentAsync(attachment.Id, "jawaid");

        if (result.Success)
        {
            // Remove from list
            CaseDto.Attachments.Remove(attachment);

            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
    }
    catch (Exception ex)
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Error",
            Content = $"Error deleting attachment: {ex.Message}",
            CssClass = "e-error",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 5000
        });
    }
}

//private static string FormatFileSize(long bytes)
//{
//    if (bytes == 0) return "0 B";

//    string[] sizes = { "B", "KB", "MB", "GB" };
//    int order = 0;
//    double size = bytes;

//    while (size >= 1024 && order < sizes.Length - 1)
//    {
//        order++;
//        size /= 1024;
//    }

//    return $"{size:0.##} {sizes[order]}";
//}

#endregion

}