@page "/court-cases/new"
@page "/court-cases/edit/{Id:int}"
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using JangLegal.Components.CourtCase
@rendermode InteractiveServer
@inject CourtCaseService CaseService
@inject PlaintiffTypeService PlaintiffTypeService
@inject NavigationManager NavMgr
@inject LawFirmDataService LawFirmService
@inject CourtDataService CourtService
@inject CaseVerdictService VerdictService
@inject CaseCategoryService CategoryService
@inject CaseEmployeeService EmployeeService
@inject CaseNatureService CaseNatureService
@inject AttachmentService AttachmentService
@inject CaseProceedingDataService ProceedingService
@inject IJSRuntime JSRuntime

<link href="~/css/proceeding-styles.css" rel="stylesheet" />

<style>
    .e-accordion .e-acrdn-item.e-select > .e-acrdn-header{
        background-color: #e0e0e0
    }
    .e-accordion .e-acrdn-item .e-acrdn-header .e-acrdn-header-content {
        width: calc(100% - 30px) !important;
    }
    .e-accordion .e-acrdn-item.e-select.e-selected.e-expand-state > .e-acrdn-header:hover, .e-accordion .e-acrdn-item.e-select.e-expand-state > .e-acrdn-header:hover {
        background-color: #e0e0e0
    }
</style>
<div class="d-flex justify-content-between align-items-center mb-3">
    <h3>@(Id == null ? "New Court Case" : "Edit Court Case")</h3>
    <SfButton OnClick="@(() => NavMgr.NavigateTo("/court-cases", true))" CssClass="e-outline">Back to List</SfButton>
</div>
<SfToast @ref="ToastObj"/>
<SfDialog ShowCloseIcon="true" CloseOnEscape="true" Width="95vw" Height="95vw" IsModal="true" @ref="dlgPreview" Visible="false">
    <DialogTemplates>
        <Header>Case Document Preview</Header>
        <Content>
            @if (!string.IsNullOrEmpty(_documentLink))
            {
                <embed src="@_documentLink" type="@GetMimeType(_documentLink)" width="100%" height="100%" style="object-fit:contain" />
            }
            else
            {
                <p>No document available for preview.</p>
            }
        </Content>
    </DialogTemplates>
</SfDialog>

<EditForm Model="CaseDto" OnValidSubmit="SaveCase" FormName="court_case_form">
<DataAnnotationsValidator/>
<ValidationSummary/>

<!-- Basic Case Information Component -->
<CaseBasicInfo CaseDto="CaseDto"
               Courts="Courts"
               Categories="Categories"
               CaseNatures="CaseNatures" />

<!-- Case Pray Component -->
<CasePray CaseDto="CaseDto" />

<!-- Case Synopsis Component -->
<CaseSynopsis CaseDto="CaseDto" />

<!-- Plaintiff Management Component -->
<PlaintiffManagement CaseDto="CaseDto"
                     CaseId="Id"
                     PlaintiffTypes="_plaintiffTypes"
                     Employees="_employees"
                     GroupCompanyCouncils="_groupCompanyCouncils"
                     NonGroupCompanyCouncils="_nonGroupCompanyCouncils"
                     LawFirms="_lawFirms"
                     Lawyers="_lawyers"
                     OnDataChanged="StateHasChanged" />

<!-- Respondent Management Component -->
<RespondentManagement CaseDto="CaseDto"
                      LawFirms="LawFirms"
                      Lawyers="Lawyers"
                      OnDataChanged="StateHasChanged" />

<!-- Attachment Management Component -->
<AttachmentManagement CaseDto="CaseDto"
                      CaseId="Id"
                      AttachmentTypes="_attachmentTypes"
                      OnShowToast="ShowToastMessage"
                      OnDataChanged="StateHasChanged" />

<div class="d-flex gap-2 justify-content-end mb-3">
    <SfButton Type="submit" CssClass="e-primary">Save Case</SfButton>
    <SfButton OnClick="@(() => NavMgr.NavigateTo("/court-cases", true))" type="button"
              CssClass="e-outline">
        Back to Cases
    </SfButton>
</div>

<!-- Proceeding Management Component -->
<ProceedingManagement CaseDto="CaseDto"
                      CaseId="Id"
                      OnOpenProceedingDialog="OpenProceedingDialog"
                      OnEditProceeding="EditProceeding"
                      OnDeleteProceeding="DeleteProceeding"
                      OnManageOutcomes="ManageOutcomes"
                      OnManageAttendees="ManageAttendees"
                      OnManageDocuments="ManageDocuments" />




</EditForm>


<!-- Verdict Selector Component -->
<VerdictSelector @bind-IsVisible="_isVerdictSelectorOpen"
                 Verdicts="Verdicts"
                 OnVerdictSelected="SelectVerdict" />









<!-- Proceeding Form Component -->
<ProceedingForm @bind-IsVisible="_isProceedingDialogOpen"
                SelectedProceeding="_selectedProceeding"
                ProceedingTypes="_proceedingTypes"
                ProceedingStatuses="_proceedingStatuses"
                OnSave="SaveProceeding" />






@if (ShowOverlay)
    {
        <div class="overlay">
            <div class="overlay-content">
                <p>Please wait...</p>
                <div class="spinner"></div>
            </div>
        </div>
    }


@code
{
    [Parameter] public int? Id { get; set; }
    private bool ShowOverlay = true;
    private string? _documentLink; // Your variable to hold the document link
    private SfDialog? dlgPreview;

    private List<LawFirmDto> LawFirms { get; set; } = new();
    private SfToast? ToastObj;
    private List<LawyerDto> Lawyers { get; set; } = new();

    private CourtCaseDto CaseDto { get; set; } = new() { Plaintiffs = new List<PlaintiffDto>(), Respondents = new List<RespondentDto>(), CaseProceedings = new List<CaseProceedingDto>() };
    private List<CourtDto> Courts { get; set; } = new();
    private List<CaseCategoryDto> Categories { get; set; } = new();
    private List<CaseNatureDto> CaseNatures { get; set; } = new();
    private List<CaseVerdictDto> Verdicts { get; set; } = new();

    // Data for components
    private List<CaseEmployeeDto> _employees = new();
    private List<CouncilOfGroupCompanyDto> _groupCompanyCouncils = new();
    private List<CouncilOfNonGroupCompanyDto> _nonGroupCompanyCouncils = new();
    private List<PlaintiffTypeDto> _plaintiffTypes = new();
    private List<LawFirmDto> _lawFirms = new();
    private List<LawyerDto> _lawyers = new();
    private List<AttachmentTypeDto> _attachmentTypes = new();

    // Dialog states for remaining functionality
    private bool _isPrayDialogOpen;
    private bool _isSynopsisSelectorOpen;
    private bool _isVerdictSelectorOpen;
    private bool _isProceedingDialogOpen;
    private bool _isOutcomeDialogOpen;
    private bool _isAttendeeDialogOpen;
    private bool _isDocumentDialogOpen;
    private bool _isOutcomeFormDialogOpen;
    private bool _isAttendeeFormDialogOpen;
    private bool _isDocumentFormDialogOpen;

    // Proceeding-related variables
    private CaseProceedingDto _selectedProceeding = new();
    private List<ProceedingTypeDto> _proceedingTypes = new();
    private List<ProceedingStatusDto> _proceedingStatuses = new();
    private List<RoleInProceedingDto> _roleInProceedings = new();
    private List<OutcomeTypeDto> _outcomeTypes = new();

    // Outcome management variables
    private ProceedingOutcomeDto _selectedOutcome = new();
    private int _currentProceedingId;
    private List<ProceedingOutcomeDto> _currentOutcomes = new();

    // Attendee management variables
    private ProceedingAttendeeDto _selectedAttendee = new();
    private List<ProceedingAttendeeDto> _currentAttendees = new();

    // Document management variables
    private ProceedingDocumentDto _selectedDocument = new();
    private List<ProceedingDocumentDto> _currentDocuments = new();
    private InputFileChangeEventArgs? _selectedFileArgs;

    // Document file upload variables
    private IBrowserFile? _selectedDocumentFile;
    private string _documentFileValidationMessage = string.Empty;


    protected override async Task OnParametersSetAsync()
    {
        await LoadReferenceData();
        if (Id.HasValue)
        {
            await LoadCase();
        }
    }

    private async Task LoadReferenceData()
    {
        Courts = await CourtService.GetCourtsAsync();
        Categories = await CategoryService.GetCategoriesAsync();
        CaseNatures = await CaseNatureService.GetCaseNaturesAsync();

        Verdicts = await VerdictService.GetVerdictsAsync();
        _employees = await EmployeeService.GetCaseEmployeesAsync();
        _groupCompanyCouncils = await CaseService.GetGroupCompanyCouncilsAsync();
        _nonGroupCompanyCouncils = await CaseService.GetNonGroupCompanyCouncilsAsync();
        LawFirms = await LawFirmService.GetLawFirmsAsync();
        Lawyers = await LawFirmService.GetLawyersAsync();
        _plaintiffTypes = await PlaintiffTypeService.GetPlaintiffTypesAsync();
        _lawFirms = await LawFirmService.GetLawFirmsAsync();
        _lawyers = await LawFirmService.GetLawyersAsync();
        _attachmentTypes = await AttachmentService.GetAttachmentTypesAsync();

        // Load proceeding-related data
        _proceedingTypes = await ProceedingService.GetProceedingTypesAsync();
        _proceedingStatuses = await ProceedingService.GetProceedingStatusesAsync();
        _roleInProceedings = await ProceedingService.GetRoleInProceedingsAsync();
        _outcomeTypes = await ProceedingService.GetOutcomeTypesAsync();
    }

    private async Task LoadCase()
    {
        ShowOverlay=true;
        if (Id.HasValue)
        {
            var loadedCase = await CaseService.GetCourtCaseByIdAsync(Id.Value);
            if (loadedCase != null)
            {
                CaseDto = loadedCase;
            }
        }
        ShowOverlay=false;
    }

    private async Task SaveCase()
    {
        ShowOverlay = true;
        var result = await CaseService.SaveCourtCaseAsync(CaseDto, "jawaid");
        if (result == "OK")
        {
            // display success message by using toastobj
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = "Case saved successfully",
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        else
        {
            // display error message
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        ShowOverlay = false;
    }

    private async Task ShowToastMessage(string message)
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Info",
            Content = message,
            CssClass = "e-info",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 3000
        });
    }




    #region Synopsis Management

    private void OpenSynopsisSelector()
    {
        _isSynopsisSelectorOpen = true;
    }

    #endregion

    #region Verdict Management

    private void OpenVerdictSelector()
    {
        _isVerdictSelectorOpen = true;
    }

    private void RemoveVerdict()
    {
        CaseDto.CaseVerdictId = null;
        CaseDto.CaseVerdictTitle = "";
    }

    private void SelectVerdict(CaseVerdictDto verdict)
    {
        CaseDto.CaseVerdictId = verdict.Id;
        CaseDto.CaseVerdictTitle = verdict.Title;
        _isVerdictSelectorOpen = false;
    }

    #endregion

    #region Proceeding Management

    private void OpenProceedingDialog()
    {
        _selectedProceeding = new CaseProceedingDto
        {
            CourtCaseId = Id ?? 0,
            ProceedingDate = DateTime.Today
        };
        _isProceedingDialogOpen = true;
    }

    private void EditProceeding(CaseProceedingDto proceeding)
    {
        _selectedProceeding = new CaseProceedingDto
        {
            ProceedingId = proceeding.ProceedingId,
            CourtCaseId = proceeding.CourtCaseId,
            ProceedingDate = proceeding.ProceedingDate,
            PresidingJudge = proceeding.PresidingJudge,
            CourtRoomNumber = proceeding.CourtRoomNumber,
            NextProceedingDate = proceeding.NextProceedingDate,
            Remarks = proceeding.Remarks,
            ProceedingTypeId = proceeding.ProceedingTypeId,
            ProceedingStatusId = proceeding.ProceedingStatusId
        };
        _isProceedingDialogOpen = true;
    }

    private async Task SaveProceeding()
    {
        ShowOverlay = true;
        var result = await ProceedingService.SaveProceedingAsync(_selectedProceeding, "jawaid");
        if (result.Success)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

            // Reload case data to refresh proceedings
            await LoadCase();
            _isProceedingDialogOpen = false;
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        ShowOverlay = false;
    }

    private async Task DeleteProceeding(int proceedingId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this proceeding? This will also delete all associated outcomes, attendees, and documents.");
        if (confirmed)
        {
            var result = await ProceedingService.DeleteProceedingAsync(proceedingId, "jawaid");
            if (result.Success)
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = result.Message,
                    CssClass = "e-success",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });

                // Reload case data to refresh proceedings
                await LoadCase();
            }
            else
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = result.Message,
                    CssClass = "e-error",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });
            }
        }
    }

    // These methods are now handled by the ProceedingManagement component
    private Task ManageOutcomes(int proceedingId)
    {
        // TODO: Implement outcome management dialog
        return Task.CompletedTask;
    }

    private Task ManageAttendees(int proceedingId)
    {
        // TODO: Implement attendee management dialog
        return Task.CompletedTask;
    }

    private Task ManageDocuments(int proceedingId)
    {
        // TODO: Implement document management dialog
        return Task.CompletedTask;
    }

    #region Outcome Management Methods

    private void AddNewOutcome(int proceedingId)
    {
        _selectedOutcome = new ProceedingOutcomeDto
        {
            ProceedingId = proceedingId
        };
        _isOutcomeFormDialogOpen = true;
    }

    private void EditOutcome(ProceedingOutcomeDto outcome)
    {
        _selectedOutcome = new ProceedingOutcomeDto
        {
            OutcomeId = outcome.OutcomeId,
            ProceedingId = outcome.ProceedingId,
            OutcomeDescription = outcome.OutcomeDescription,
            OrderIssuedBy = outcome.OrderIssuedBy,
            OrderDate = outcome.OrderDate,
            DocumentReference = outcome.DocumentReference,
            OutcomeTypeId = outcome.OutcomeTypeId
        };
        _isOutcomeFormDialogOpen = true;
    }

    private async Task SaveOutcome()
    {
        ShowOverlay = true;
        var result = await ProceedingService.SaveOutcomeAsync(_selectedOutcome, "jawaid");
        if (result.Success)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

            // Reload case data to refresh outcomes
            await LoadCase();
            _isOutcomeFormDialogOpen = false;

            // Refresh current outcomes list
            var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
            _currentOutcomes = proceeding?.ProceedingOutcomes ?? new List<ProceedingOutcomeDto>();
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        ShowOverlay = false;
    }

    private async Task DeleteOutcome(int outcomeId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this outcome?");
        if (confirmed)
        {
            var result = await ProceedingService.DeleteOutcomeAsync(outcomeId, "jawaid");
            if (result.Success)
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = result.Message,
                    CssClass = "e-success",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });

                // Reload case data to refresh outcomes
                await LoadCase();

                // Refresh current outcomes list
                var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
                _currentOutcomes = proceeding?.ProceedingOutcomes ?? new List<ProceedingOutcomeDto>();
            }
            else
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = result.Message,
                    CssClass = "e-error",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });
            }
        }
    }

    #endregion

    #region Attendee Management Methods

    private void AddNewAttendee(int proceedingId)
    {
        _selectedAttendee = new ProceedingAttendeeDto
        {
            ProceedingId = proceedingId,
            IsPresent = true
        };
        _isAttendeeFormDialogOpen = true;
    }

    private void EditAttendee(ProceedingAttendeeDto attendee)
    {
        _selectedAttendee = new ProceedingAttendeeDto
        {
            AttendeeId = attendee.AttendeeId,
            ProceedingId = attendee.ProceedingId,
            PersonName = attendee.PersonName,
            IsPresent = attendee.IsPresent,
            RoleInProceedingId = attendee.RoleInProceedingId
        };
        _isAttendeeFormDialogOpen = true;
    }

    private async Task SaveAttendee()
    {
        ShowOverlay = true;
        var result = await ProceedingService.SaveAttendeeAsync(_selectedAttendee, "jawaid");
        if (result.Success)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

            // Reload case data to refresh attendees
            await LoadCase();
            _isAttendeeFormDialogOpen = false;

            // Refresh current attendees list
            var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
            _currentAttendees = proceeding?.ProceedingAttendees ?? new List<ProceedingAttendeeDto>();
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
        ShowOverlay = !true;
    }

    private async Task DeleteAttendee(int attendeeId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this attendee?");
        if (confirmed)
        {
            var result = await ProceedingService.DeleteAttendeeAsync(attendeeId, "jawaid");
            if (result.Success)
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = result.Message,
                    CssClass = "e-success",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });

                // Reload case data to refresh attendees
                await LoadCase();

                // Refresh current attendees list
                var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
                _currentAttendees = proceeding?.ProceedingAttendees ?? new List<ProceedingAttendeeDto>();
            }
            else
            {
                await ToastObj!.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = result.Message,
                    CssClass = "e-error",
                    ShowCloseButton = true,
                    ShowProgressBar = true,
                    Timeout = 5000
                });
            }
        }
    }

    #endregion

    #region Document Management Methods

    private int _proceedingId = 0;
    private void AddNewDocument(int proceedingId)
    {
        _proceedingId = proceedingId;
        _selectedDocument = new ProceedingDocumentDto
        {
            ProceedingId = proceedingId,
            SubmissionDate = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day)
        };
        _selectedFileArgs = null;
        _selectedDocumentFile = null;
        _documentFileValidationMessage = string.Empty;
        _isDocumentFormDialogOpen = true;
    }

    @* private void OnFileSelected(InputFileChangeEventArgs e)
            {
                _selectedFileArgs = e;
            } *@

private async Task SaveDocument()
{
    ShowOverlay = true;
    if (_selectedDocumentFile == null)
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Error",
            Content = "Please select a file to upload",
            CssClass = "e-error",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 5000
        });
        return;
    }

    var result = await ProceedingService.SaveDocumentAsync(
        _selectedDocumentFile,
        _selectedDocument,
        Id ?? 0 ,
        _proceedingId,
        "jawaid");

    if (result.Success)
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Success",
            Content = result.Message,
            CssClass = "e-success",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 5000
        });

        // Reload case data to refresh documents
        await LoadCase();
        _isDocumentFormDialogOpen = false;

        // Refresh current documents list
        var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
        _currentDocuments = proceeding?.ProceedingDocuments ?? new List<ProceedingDocumentDto>();
    }
    else
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Error",
            Content = result.Message,
            CssClass = "e-error",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 5000
        });
    }
    ShowOverlay = !true;
}

private async Task DeleteDocument(int documentId)
{
    var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this document?");
    if (confirmed)
    {
        var result = await ProceedingService.DeleteDocumentAsync(documentId, "jawaid");
        if (result.Success)
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Success",
                Content = result.Message,
                CssClass = "e-success",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });

            // Reload case data to refresh documents
            await LoadCase();

            // Refresh current documents list
            var proceeding = CaseDto.CaseProceedings.FirstOrDefault(p => p.ProceedingId == _currentProceedingId);
            _currentDocuments = proceeding?.ProceedingDocuments ?? new List<ProceedingDocumentDto>();
        }
        else
        {
            await ToastObj!.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = result.Message,
                CssClass = "e-error",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            });
        }
    }
}


private async Task DownloadDocument(int documentId)
{
    var result = await ProceedingService.DownloadDocumentAsync(documentId);
    if (result is { Success: true, FileData: not null })
    {
        await JSRuntime.InvokeVoidAsync("downloadFile", result.FileName, Convert.ToBase64String(result.FileData));
    }
    else
    {
        await ToastObj!.ShowAsync(new ToastModel
        {
            Title = "Error",
            Content = "Failed to download document",
            CssClass = "e-error",
            ShowCloseButton = true,
            ShowProgressBar = true,
            Timeout = 5000
        });
    }
}

private string FormatFileSize(long bytes)
{
    if (bytes == 0) return "0 B";

    string[] sizes = new[] { "B", "KB", "MB", "GB" };
    var order = 0;
    double size = bytes;

    while (size >= 1024 && order < sizes.Length - 1)
    {
        order++;
        size /= 1024;
    }

    return $"{size:0.##} {sizes[order]}";
}

#endregion

#endregion



//private async Task OnGroupCouncilChange(ChangeEventArgs<int?, CouncilOfGroupCompanyDto> args)
//{
//    if (args.Value.HasValue)
//    {
//        var council = _groupCompanyCouncils.FirstOrDefault(c => c.Id == args.Value);
//        if (council != null)
//        {
//            _selectedPlaintiff.CouncilOfGroupCompanyName = council.Name;
//        }
//    }
//    else
//    {
//        _selectedPlaintiff.CouncilOfGroupCompanyName = string.Empty;
//    }
//}

//private async Task OnNonGroupCouncilChange(ChangeEventArgs<int?, CouncilOfNonGroupCompanyDto> args)
//{
//    if (args.Value.HasValue)
//    {
//        var council = _nonGroupCompanyCouncils.FirstOrDefault(c => c. == args.Value);
//        if (council != null)
//        {
//            _selectedPlaintiff.CouncilOfNonGroupCompanyName = council.Name;
//        }
//    }
//    else
//    {
//        _selectedPlaintiff.CouncilOfNonGroupCompanyName = string.Empty;
//    }
//}











public async Task ShowDocumentPreview(string documentLink)
    {
        _documentLink = documentLink;
        if (dlgPreview != null)
        {
            await dlgPreview.ShowAsync();
        }
    }

    // Helper method to determine the MIME type based on the file extension
    private string GetMimeType(string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return "application/octet-stream"; // Default generic binary type
        }

        string extension = Path.GetExtension(fileName).ToLowerInvariant();

        return extension switch
        {
            ".pdf" => "application/pdf",
            ".jpg" => "image/jpeg",
            ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            // Add more image types as needed
            _ => "application/octet-stream", // Fallback for unknown types
        };
    }






}