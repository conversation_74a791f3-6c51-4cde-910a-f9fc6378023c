@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using JangLegal.DTO

<!-- Proceeding Form Dialog -->
<SfDialog @bind-Visible="IsVisible" Width="800px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>@(SelectedProceeding.ProceedingId == 0 ? "Add Proceeding" : "Edit Proceeding")</Header>
        <Content>
            <EditForm Model="SelectedProceeding" OnValidSubmit="SaveProceeding">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDatePicker TValue="DateTime" @bind-Value="SelectedProceeding.ProceedingDate"
                                      Placeholder="Proceeding Date"
                                      FloatLabelType="FloatLabelType.Always">
                        </SfDatePicker>
                        <ValidationMessage For="@(() => SelectedProceeding.ProceedingDate)"/>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TItem="ProceedingTypeDto" TValue="int?"
                                        @bind-Value="SelectedProceeding.ProceedingTypeId"
                                        DataSource="@ProceedingTypes"
                                        Placeholder="Select Proceeding Type"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Text="TypeName" Value="ProceedingTypeId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TItem="ProceedingStatusDto" TValue="int?"
                                        @bind-Value="SelectedProceeding.ProceedingStatusId"
                                        DataSource="@ProceedingStatuses"
                                        Placeholder="Select Proceeding Status"
                                        FloatLabelType="FloatLabelType.Always"
                                        AllowFiltering="true"
                                        FilterType="FilterType.Contains">
                            <DropDownListFieldSettings Text="StatusName" Value="ProceedingStatusId"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfTextBox @bind-Value="SelectedProceeding.PresidingJudge"
                                   Placeholder="Presiding Judge"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfTextBox @bind-Value="SelectedProceeding.CourtRoomNumber"
                                   Placeholder="Court Room Number"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDatePicker TValue="DateTime?" @bind-Value="SelectedProceeding.NextProceedingDate"
                                      Placeholder="Next Proceeding Date"
                                      FloatLabelType="FloatLabelType.Always">
                        </SfDatePicker>
                    </div>
                </div>

                <div class="mb-3">
                    <SfTextBox @bind-Value="SelectedProceeding.Remarks"
                               Placeholder="Remarks"
                               Multiline="true"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => IsVisible = false)" type="button"
                              CssClass="e-outline-secondary">
                        Cancel
                    </SfButton>
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
    [Parameter] public CaseProceedingDto SelectedProceeding { get; set; } = new();
    [Parameter] public List<ProceedingTypeDto> ProceedingTypes { get; set; } = new();
    [Parameter] public List<ProceedingStatusDto> ProceedingStatuses { get; set; } = new();
    [Parameter] public EventCallback<CaseProceedingDto> OnSave { get; set; }

    private async Task SaveProceeding()
    {
        await OnSave.InvokeAsync(SelectedProceeding);
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(IsVisible);
    }
}
