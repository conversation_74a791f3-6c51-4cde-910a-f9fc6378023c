@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using JangLegal.DTO

<!-- Respondents Section -->
<div class="card mb-3">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>Respondents</h5>
            <SfButton type="button" OnClick="@(() =>
                                             {
                                                 _selectedRespondent = new RespondentDto();
                                                 _isRespondentDialogOpen = true;
                                             })" CssClass="e-primary">
                Add Respondent
            </SfButton>
        </div>

        @if (CaseDto.Respondents.Any())
        {
            <SfGrid DataSource="CaseDto.Respondents" AllowPaging="true" AllowSorting="true" AllowTextWrap="true"
                    AllowFiltering="true">
                <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn Field="@nameof(RespondentDto.Name)" HeaderText="Name" Width="250px"></GridColumn>
                    <GridColumn Field="@nameof(RespondentDto.LawFirmName)" HeaderText="Law Firm" Width="250px"></GridColumn>
                    <GridColumn Field="@nameof(RespondentDto.LawyerName)" HeaderText="Lawyer" Width="250px"></GridColumn>
                    <GridColumn HeaderText="Actions" AutoFit="true">
                        <Template Context="gridContext">
                            @{
                                if (gridContext is RespondentDto respondent)
                                {
                                    <SfButton OnClick="@(() => EditRespondent(respondent))"
                                              type="button"
                                              CssClass="e-primary e-small">
                                        Edit
                                    </SfButton>
                                    <SfButton OnClick="@(() => RemoveRespondent(respondent))"
                                              type="button"
                                              CssClass="e-danger e-small">
                                        Remove
                                    </SfButton>
                                }
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

<!-- Respondent Form Dialog -->
<SfDialog Width="800px" IsModal="true" ShowCloseIcon="true" @bind-Visible="_isRespondentDialogOpen">
    <DialogTemplates>
        <Header>Respondent Detail</Header>
        <Content>
            <EditForm Model="_selectedRespondent" OnValidSubmit="SaveRespondent">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="row">
                    <div class="col-md-12 mb-3">
                        <SfTextBox @bind-Value="_selectedRespondent.Name"
                                   Placeholder="Name"
                                   FloatLabelType="FloatLabelType.Always">
                        </SfTextBox>
                        <ValidationMessage For="@(() => _selectedRespondent.Name)"/>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TValue="int?" TItem="LawFirmDto"
                                        @bind-Value="_selectedRespondent.LawFirmId"
                                        DataSource="@LawFirms"
                                        Placeholder="Select Law Firm"
                                        FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="Title" Value="Id"/>
                            <DropDownListEvents TValue="int?" TItem="LawFirmDto"
                                                ValueChange="@(e => OnLawFirmChange(e))">
                            </DropDownListEvents>
                        </SfDropDownList>
                    </div>
                    <div class="col-md-6 mb-3">
                        <SfDropDownList TValue="int?" TItem="LawyerDto"
                                        @bind-Value="_selectedRespondent.LawyerId"
                                        DataSource="@FilteredLawyers"
                                        Placeholder="Select Lawyer"
                                        FloatLabelType="FloatLabelType.Always"
                                        Enabled="@(_selectedRespondent.LawFirmId.HasValue)">
                            <DropDownListFieldSettings Text="Name" Value="Id"/>
                        </SfDropDownList>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => _isRespondentDialogOpen = false)" type="button"
                              CssClass="e-outline-secondary">
                        Cancel
                    </SfButton>
                    <SfButton Type="submit" CssClass="e-primary">Save</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [Parameter] public CourtCaseDto CaseDto { get; set; } = new();
    [Parameter] public List<LawFirmDto> LawFirms { get; set; } = new();
    [Parameter] public List<LawyerDto> Lawyers { get; set; } = new();
    [Parameter] public EventCallback OnDataChanged { get; set; }

    private bool _isRespondentDialogOpen;
    private RespondentDto _selectedRespondent = new();

    private List<LawyerDto> FilteredLawyers => _selectedRespondent.LawFirmId.HasValue
        ? Lawyers.Where(l => l.LawFirmId == _selectedRespondent.LawFirmId).ToList()
        : new List<LawyerDto>();

    private void EditRespondent(RespondentDto resp)
    {
        _selectedRespondent = new RespondentDto
        {
            Id = resp.Id,
            Name = resp.Name,
            CourtCaseId = resp.CourtCaseId,
            LawFirmId = resp.LawFirmId,
            LawFirmName = resp.LawFirmName,
            LawyerId = resp.LawyerId,
            LawyerName = resp.LawyerName
        };
        _isRespondentDialogOpen = true;
    }

    private void RemoveRespondent(RespondentDto resp)
    {
        CaseDto.Respondents = CaseDto.Respondents.Where(c => c.Id != resp.Id).ToList();
        OnDataChanged.InvokeAsync();
    }

    private void SaveRespondent()
    {
        if (_selectedRespondent.LawFirmId.HasValue)
        {
            var lawFirm = LawFirms.First(f => f.Id == _selectedRespondent.LawFirmId);
            _selectedRespondent.LawFirmName = lawFirm.Title;
        }
        else
        {
            _selectedRespondent.LawFirmName = "";
        }

        if (_selectedRespondent.LawyerId.HasValue)
        {
            var lawyer = Lawyers.FirstOrDefault(l => l.Id == _selectedRespondent.LawyerId);
            _selectedRespondent.LawyerName = lawyer == null ? "" : lawyer.Name;
        }
        else
        {
            _selectedRespondent.LawyerName = "";
        }

        var rs = CaseDto.Respondents.FirstOrDefault(c => c.Id == _selectedRespondent.Id);
        if (rs != null)
        {
            rs.LawyerName = _selectedRespondent.LawyerName;
            rs.LawyerId = _selectedRespondent.LawyerId;
            rs.LawFirmId = _selectedRespondent.LawFirmId;
            rs.LawFirmName = _selectedRespondent.LawFirmName;
            rs.Name = _selectedRespondent.Name;
        }
        else
        {
            rs = new RespondentDto
            {
                LawFirmName = _selectedRespondent.LawFirmName,
                LawFirmId = _selectedRespondent.LawFirmId,
                Id = _selectedRespondent.Id,
                LawyerId = _selectedRespondent.LawyerId,
                LawyerName = _selectedRespondent.LawyerName,
                Name = _selectedRespondent.Name
            };

            CaseDto.Respondents.Add(rs);
            CaseDto.Respondents = CaseDto.Respondents.OrderBy(c => c.Name).ToList();
        }

        _isRespondentDialogOpen = false;
        OnDataChanged.InvokeAsync();
    }

    private void OnLawFirmChange(ChangeEventArgs<int?, LawFirmDto> args)
    {
        _selectedRespondent.LawFirmId = args.Value;
        _selectedRespondent.LawyerId = null;
        StateHasChanged();
    }
}
