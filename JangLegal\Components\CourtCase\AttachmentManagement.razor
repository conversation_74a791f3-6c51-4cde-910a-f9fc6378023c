@using JangLegal.DTO
@inject IJSRuntime JSRuntime

<!-- Attachments Section - Only visible when editing -->
@if (CaseId.HasValue)
{
    <div class="card mb-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>Case Attachments</h5>
                <SfButton type="button" OnClick="OpenAttachmentDialog" CssClass="e-primary">Add Attachment</SfButton>
            </div>

            @if (CaseDto.Attachments.Any())
            {
                <SfGrid DataSource="CaseDto.Attachments" AllowPaging="true" AllowSorting="true" AllowTextWrap="true"
                        AllowFiltering="true">
                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                    <GridColumns>
                        <GridColumn Field="@nameof(AttachmentDto.FileName)" HeaderText="File Name" Width="250px"></GridColumn>
                        <GridColumn Field="@nameof(AttachmentDto.AttachmentTypeName)" HeaderText="Type" Width="150px"></GridColumn>
                        <GridColumn Field="@nameof(AttachmentDto.CreatedDate)" HeaderText="Upload Date" Width="150px" Format="d"></GridColumn>
                        <GridColumn Field="@nameof(AttachmentDto.CreatedBy)" HeaderText="Uploaded By" Width="150px"></GridColumn>
                        <GridColumn HeaderText="Actions" Width="200px">
                            <Template Context="gridContext">
                                @{
                                    if (gridContext is AttachmentDto attachment)
                                    {
                                        <SfButton OnClick="@(() => DownloadAttachment(attachment))"
                                                  type="button"
                                                  CssClass="e-info e-small">
                                            Download
                                        </SfButton>
                                        
                                        <SfButton OnClick="@(() => DeleteAttachment(attachment))"
                                                  type="button"
                                                  CssClass="e-danger e-small">
                                            Delete
                                        </SfButton>
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
            else
            {
                <p class="text-muted">No attachments uploaded yet.</p>
            }
        </div>
    </div>
}

<!-- Attachment Upload Dialog -->
<SfDialog @bind-Visible="_isAttachmentDialogOpen" Width="600px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>Add Attachment</Header>
        <Content>
            <EditForm Model="_selectedAttachment" OnValidSubmit="SaveAttachment">
                <DataAnnotationsValidator/>
                <ValidationSummary/>

                <div class="mb-3">
                    <label class="form-label">Attachment Type</label>
                    <SfDropDownList TItem="AttachmentTypeDto" TValue="int?"
                                    @bind-Value="_selectedAttachment.AttachmentTypeId"
                                    DataSource="@AttachmentTypes"
                                    Placeholder="Select Attachment Type"
                                    FloatLabelType="FloatLabelType.Always"
                                    AllowFiltering="true"
                                    FilterType="FilterType.Contains">
                        <DropDownListFieldSettings Text="Title" Value="Id"></DropDownListFieldSettings>
                    </SfDropDownList>
                    <ValidationMessage For="@(() => _selectedAttachment.AttachmentTypeId)"/>
                </div>

                <div class="mb-3">
                    <label class="form-label">Custom File Name (Optional)</label>
                    <SfTextBox @bind-Value="_customFileName"
                               Placeholder="Enter custom file name (leave empty to use original name)"
                               FloatLabelType="FloatLabelType.Always">
                    </SfTextBox>
                    <small class="text-muted">If provided, this name will be used instead of the original file name</small>
                </div>

                <div class="mb-3">
                    <label class="form-label">Select File</label>
                    <div class="file-upload-container">
                        <InputFile OnChange="OnFileSelected"
                                   class="file-input"
                                   id="fileInput"
                                   accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.bmp"/>
                        <label for="fileInput" class="file-upload-label">
                            <i class="material-icons">cloud_upload</i>
                            <span>Choose File</span>
                        </label>
                    </div>

                    @if (!string.IsNullOrEmpty(_fileValidationMessage))
                    {
                        <div class="text-danger mt-2">
                            <i class="material-icons">error</i>
                            @_fileValidationMessage
                        </div>
                    }

                    @if (_selectedFile != null)
                    {
                        <div class="file-preview mt-3">
                            <div class="file-info">
                                <i class="material-icons">description</i>
                                <div class="file-details">
                                    <div class="file-name">@_selectedFile.Name</div>
                                    <div class="file-size">@FormatFileSize(_selectedFile.Size)</div>
                                </div>
                                <button type="button" class="btn-remove" @onclick="ClearSelectedFile">
                                    <i class="material-icons">close</i>
                                </button>
                            </div>
                        </div>
                    }

                    <div class="file-requirements mt-2">
                        <small class="text-muted">
                            <i class="material-icons">info</i>
                            Allowed: PDF, Word, Excel, Images • Max size: 10MB
                        </small>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <SfButton OnClick="@(() => _isAttachmentDialogOpen = false)" type="button"
                              CssClass="e-outline-secondary">
                        Cancel
                    </SfButton>
                    <SfButton Type="submit" CssClass="e-primary" Disabled="@(_selectedFile == null || _selectedAttachment.AttachmentTypeId == null)">Upload</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [Parameter] public CourtCaseDto CaseDto { get; set; } = new();
    [Parameter] public int? CaseId { get; set; }
    [Parameter] public List<AttachmentTypeDto> AttachmentTypes { get; set; } = new();
    [Parameter] public EventCallback<string> OnShowToast { get; set; }
    [Parameter] public EventCallback OnDataChanged { get; set; }

    private bool _isAttachmentDialogOpen;
    private AttachmentDto _selectedAttachment = new();
    private IBrowserFile? _selectedFile;
    private string _fileValidationMessage = string.Empty;
    private string _customFileName = string.Empty;

    private void OpenAttachmentDialog()
    {
        _selectedAttachment = new AttachmentDto
        {
            CourtCaseId = CaseId ?? 0
        };
        _selectedFile = null;
        _fileValidationMessage = string.Empty;
        _customFileName = string.Empty;
        _isAttachmentDialogOpen = true;
    }

    private void OnFileSelected(InputFileChangeEventArgs e)
    {
        _selectedFile = e.File;
        _fileValidationMessage = string.Empty;

        // Validate file
        if (_selectedFile != null)
        {
            var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
            var extension = Path.GetExtension(_selectedFile.Name).ToLowerInvariant();

            if (!allowedExtensions.Contains(extension))
            {
                _fileValidationMessage = $"File type not allowed. Allowed types: {string.Join(", ", allowedExtensions)}";
                _selectedFile = null;
                return;
            }

            if (_selectedFile.Size > 10 * 1024 * 1024) // 10MB
            {
                _fileValidationMessage = "File size cannot exceed 10 MB";
                _selectedFile = null;
            }
        }
    }

    private void ClearSelectedFile()
    {
        _selectedFile = null;
        _fileValidationMessage = string.Empty;
    }

    private async Task SaveAttachment()
    {
        if (_selectedFile == null || _selectedAttachment.AttachmentTypeId == null)
        {
            return;
        }

        await OnShowToast.InvokeAsync("Uploading file...");
        // The actual save logic will be handled by the parent component
        // This component just manages the UI
        _isAttachmentDialogOpen = false;
        await OnDataChanged.InvokeAsync();
    }

    private async Task DownloadAttachment(AttachmentDto attachment)
    {
        await JSRuntime.InvokeVoidAsync("open", attachment.FileUrl, "_blank");
    }

    private async Task DeleteAttachment(AttachmentDto attachment)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this attachment?");
        if (confirmed)
        {
            // Remove from list
            CaseDto.Attachments.Remove(attachment);
            await OnDataChanged.InvokeAsync();
        }
    }

    private static string FormatFileSize(long bytes)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = new[] { "B", "KB", "MB", "GB" };
        var order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }
}
