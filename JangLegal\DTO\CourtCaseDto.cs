namespace JangLegal.DTO;

public class CourtCaseDto
{
    public int Id { get; set; }
    public string CaseNumber { get; set; }
    public string Title { get; set; }
    public int CourtId { get; set; }
    public string CourtName { get; set; }
    public int? CaseFilingYear { get; set; }
    public string? Pray { get; set; }

    public string? CaseSynopsis { get; set; }

    public int? CaseVerdictId { get; set; }
    public string CaseVerdictTitle { get; set; }
    public DateTime? DateInOffice { get; set; }
    public decimal? ClaimAmount { get; set; }
    public int? CaseCategoryId { get; set; }
    public string CategoryTitle { get; set; }
    public int? StayOrderId { get; set; }
    public string StayOrderTitle { get; set; }
    public List<PlaintiffDto> Plaintiffs { get; set; } = new();
    public List<RespondentDto> Respondents { get; set; } = new();
    public List<AttachmentDto> Attachments { get; set; } = new();
    public List<CaseProceedingDto> CaseProceedings { get; set; } = new();
    public int PlaintiffCount { get; set; }
    public int RespondentCount { get; set; }
    public string? CaseNature { get; set; }
    public int? CaseNatureId { get; set; }
    public string CaseNatureName { get; set; } = string.Empty;
    public bool IsDecided { get; set; }
    public string IsDecidedStr => IsDecided ? "Yes" : "No";
    public bool IsDuplicateCase { get; set; }
    public string Duplicate => IsDuplicateCase ? "Duplicate" : "";
}