@page "/setup/attachment-types"
@using JangLegal.DTO
@using JangLegal.Services
@inject AttachmentTypeService Service
@inject NavigationManager NavMgr
@inject IJSRuntime js
@rendermode InteractiveServer

<div class="d-flex justify-content-between align-items-center mb-3">
    <span style="font-size:24px;font-weight:bold;">Attachment Types</span>
    <SfButton OnClick="OpenNewForm" CssClass="e-primary">Add New Attachment Type</SfButton>
</div>

<SfToast @ref="_toastObj"></SfToast>

<SfGrid DataSource="@_attachmentTypesList" AllowPaging="true" AllowSorting="true" AllowFiltering="true">
    <GridPageSettings PageSize="20"></GridPageSettings>
    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Menu"></GridFilterSettings>
    <GridColumns>
        <GridColumn Field="@nameof(AttachmentTypeDto.Id)" HeaderText="ID" Width="80" IsPrimaryKey="true"></GridColumn>
        <GridColumn Field="@nameof(AttachmentTypeDto.Title)" HeaderText="Title" Width="300"></GridColumn>
        <GridColumn HeaderText="Actions" Width="150">
            <Template>
                @{
                    var attachmentType = (context as AttachmentTypeDto)!;
                }
                <SfButton OnClick="() => OpenEditForm(attachmentType.Id)" CssClass="e-small e-info">Edit</SfButton>
                <SfButton OnClick="() => OpenDeleteDialog(attachmentType)" CssClass="e-small e-danger" style="margin-left: 5px;">Delete</SfButton>
            </Template>
        </GridColumn>
    </GridColumns>
</SfGrid>

<!-- Add/Edit Dialog -->
<SfDialog @bind-Visible="_isDialogOpen" Width="500px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>@(_selectedAttachmentType.Id == 0 ? "Add New Attachment Type" : "Edit Attachment Type")</Header>
        <Content>
            <EditForm Model="_selectedAttachmentType" OnValidSubmit="OnValidSubmit">
                <FluentValidationValidator />
                <div class="form-group mb-3">
                    <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="_selectedAttachmentType.Title" ID="title" Placeholder="Enter attachment type title" CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => _selectedAttachmentType.Title)" />
                </div>
                <div class="form-group text-end">
                    <SfButton Type="ButtonType.Button" OnClick="CloseDialog" CssClass="e-outline">Cancel</SfButton>
                    <SfButton Type="ButtonType.Submit" CssClass="e-primary" style="margin-left: 10px;">Save</SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Delete Confirmation Dialog -->
<SfDialog @bind-Visible="_isDeleteDialogOpen" Width="400px" IsModal="true" ShowCloseIcon="true">
    <DialogTemplates>
        <Header>Confirm Delete</Header>
        <Content>
            <p>Are you sure you want to delete the attachment type "<strong>@(_attachmentTypeToDelete?.Title)</strong>"?</p>
            <div class="text-end">
                <SfButton OnClick="() => _isDeleteDialogOpen = false" CssClass="e-outline">Cancel</SfButton>
                <SfButton OnClick="ConfirmDelete" CssClass="e-danger" style="margin-left: 10px;">Delete</SfButton>
            </div>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private List<AttachmentTypeDto> _attachmentTypesList = new();
    private AttachmentTypeDto _selectedAttachmentType = new();
    private AttachmentTypeDto? _attachmentTypeToDelete;
    private bool _isDialogOpen = false;
    private bool _isDeleteDialogOpen = false;
    private SfToast? _toastObj;
    private string _userId = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        // Get current user (you may need to adjust this based on your auth implementation)
        _userId = "system"; // Replace with actual user identification logic
        await LoadData();
    }

    private async Task LoadData()
    {
        _attachmentTypesList = await Service.GetAttachmentTypesAsync();
    }

    private void OpenNewForm()
    {
        _selectedAttachmentType = new AttachmentTypeDto();
        _isDialogOpen = true;
    }

    private async Task OpenEditForm(int id)
    {
        var attachmentType = await Service.GetAttachmentTypeByIdAsync(id);
        if (attachmentType != null)
        {
            _selectedAttachmentType = attachmentType;
            _isDialogOpen = true;
        }
    }

    private void CloseDialog()
    {
        _selectedAttachmentType = new AttachmentTypeDto();
        _isDialogOpen = false;
    }

    private async Task OnValidSubmit()
    {
        var result = await Service.SaveAttachmentTypeAsync(_selectedAttachmentType, _userId);
        
        if (result == "OK")
        {
            _isDialogOpen = false;
            await LoadData();
            await ShowToast("Success", "Attachment type saved successfully", "success");
        }
        else
        {
            await ShowToast("Error", result, "error");
        }
    }

    private void OpenDeleteDialog(AttachmentTypeDto attachmentType)
    {
        _attachmentTypeToDelete = attachmentType;
        _isDeleteDialogOpen = true;
    }

    private async Task ConfirmDelete()
    {
        if (_attachmentTypeToDelete != null)
        {
            var result = await Service.DeleteAttachmentTypeAsync(_attachmentTypeToDelete.Id, _userId);
            
            if (result == "OK")
            {
                _isDeleteDialogOpen = false;
                await LoadData();
                await ShowToast("Success", "Attachment type deleted successfully", "success");
            }
            else
            {
                await ShowToast("Error", result, "error");
            }
        }
    }

    private async Task ShowToast(string title, string message, string type)
    {
        if (_toastObj != null)
        {
            var toastModel = new ToastModel
            {
                Title = title,
                Content = message,
                CssClass = type == "success" ? "e-toast-success" : "e-toast-danger",
                Icon = type == "success" ? "e-success toast-icons" : "e-error toast-icons",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 3000
            };
            await _toastObj.ShowAsync(toastModel);
        }
    }
}
