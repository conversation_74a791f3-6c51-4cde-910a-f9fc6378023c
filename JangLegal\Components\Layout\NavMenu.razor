﻿@rendermode InteractiveServer

<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu" onclick="document.getElementById('navmenu-toggle').click();">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="_expanded">
            <AuthorizeView>
                <Authorized>
                    <FluentNavLink Href="/" Match="NavLinkMatch.All" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>
                    @* <FluentNavLink Href="/employees" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.NumberSymbolSquare())" IconColor="Color.Accent">Employees Directory</FluentNavLink> *@
                    <FluentNavLink Href="/court-cases" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.FolderBriefcase())" IconColor="Color.Accent">Cases</FluentNavLink>
                    <FluentNavLink Href="/search" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.Search())" IconColor="Color.Accent">Search</FluentNavLink>
                    <FluentNavLink Href="/setup/case-employees" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.PeopleTeam())" IconColor="Color.Accent">Case Employees</FluentNavLink>
                    <FluentNavLink Href="/setup/case-categories" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.Tag())" IconColor="Color.Accent">Case Categories</FluentNavLink>
                    <FluentNavLink Href="/setup/case-natures" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.BookInformation())" IconColor="Color.Accent">Case Natures</FluentNavLink>
                    @*<FluentNavLink Href="/setup/verdicts" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.Checkmark())" IconColor="Color.Accent">Verdicts</FluentNavLink>*@
                    <FluentNavLink Href="/setup/attachment-types" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.Attach())" IconColor="Color.Accent">Attachment Types</FluentNavLink>
                    <FluentNavLink Href="/setup/group-company-councils" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.Building())" IconColor="Color.Accent">Group Company Councils</FluentNavLink>
                    <FluentNavLink Href="/setup/non-group-company-councils" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.Building())" IconColor="Color.Accent">Non-Group Company Councils</FluentNavLink>
                    <FluentNavLink Href="/lawfirms" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.Building())" IconColor="Color.Accent">Law Firms</FluentNavLink>
                    <FluentNavLink Href="/lawyers" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.Person())" IconColor="Color.Accent">Lawyers</FluentNavLink>
                    <FluentNavLink Href="/courts" Icon="@(new Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.Size20.ScaleFill())" IconColor="Color.Accent">Courts</FluentNavLink>
                </Authorized>
            </AuthorizeView>
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool _expanded = true;
}
