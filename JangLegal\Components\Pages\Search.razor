@page "/search"
@rendermode InteractiveServer
@inject SearchService SearchService
@inject CourtCaseService CaseService
@inject CourtDataService CourtService
@inject CaseCategoryService CategoryService
@inject CaseNatureService CaseNatureService
@inject CaseEmployeeService EmployeeService
@inject AttachmentTypeService AttachmentTypeService
@inject NavigationManager NavMgr
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType

<PageTitle>Search Cases</PageTitle>

<style>
    .search-container {
        /*padding: 10px;*/
        max-width: 1400px;
        margin: 0 auto;
    }

    .search-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .search-header h2 {
        color: #2c3e50;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        margin-bottom: 10px;
    }

    .search-criteria-section {
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 8px;
    }

    .search-form {
        padding: 20px 0;
    }

    .search-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }

    .search-item {
        display: flex;
        flex-direction: column;
    }

    .search-item label {
        font-weight: 600;
        margin-bottom: 8px;
        color: #495057;
    }

    .checkbox-group {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .search-actions {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #dee2e6;
    }

    .search-btn {
        min-width: 120px;
    }

    .search-loading {
        text-align: center;
        padding: 40px;
    }

    .search-results-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .results-summary {
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
    }

    .results-summary h3 {
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .results-grid {
        margin-top: 15px;
    }

    @@media (max-width: 768px) {
        .search-row {
            grid-template-columns: 1fr;
        }

        .checkbox-group {
            flex-direction: column;
        }

        .search-actions {
            flex-direction: column;
            align-items: center;
        }
    }
</style>

<div class="search-container">
    <div class="search-header">
        <h2><i class="material-icons">search</i> Advance Search</h2>
        
    </div>

    <!-- Search Criteria Section -->
    <div class="search-criteria-section">
        <SfExpander>
            <ExpanderItems>
                <ExpanderItem>
                    <HeaderTemplate>
                        <div><i class="material-icons">filter_list</i> Search Filters</div>
                    </HeaderTemplate>
                    <ContentTemplate>
                        <div class="search-form">
                            <div class="search-row">
                                <div class="search-item">
                                    <label>Search Text</label>
                                    <SfTextBox @bind-Value="_searchCriteria.SearchText" 
                                              Placeholder="Search in case titles, numbers, synopsis, pray, and attachment names"
                                              CssClass="search-textbox"></SfTextBox>
                                </div>
                            </div>
                            
                            <div class="search-row">
                                <div class="search-item">
                                    <label>Search In</label>
                                    <div class="checkbox-group">
                                        <SfCheckBox @bind-Checked="_searchCriteria.SearchInCases" Label="Cases"></SfCheckBox>
                                        <SfCheckBox @bind-Checked="_searchCriteria.SearchInAttachments" Label="Attachments"></SfCheckBox>
                                        <SfCheckBox @bind-Checked="_searchCriteria.SearchInCaseContent" Label="Case Content"></SfCheckBox>
                                        <SfCheckBox @bind-Checked="_searchCriteria.SearchInAttachmentNames" Label="Attachment Names"></SfCheckBox>
                                    </div>
                                </div>
                            </div>

                            <div class="search-row">
                                <div class="search-item">
                                    <label>Employees</label>
                                    <SfMultiSelect TValue="string[]" TItem="CaseEmployeeDto"
                                                  AllowFiltering="true"
                                                  Placeholder="Select Employees"
                                                  DataSource="@_employees"
                                                  @bind-Value="_searchCriteria.EmployeeIds"
                                                  Mode="VisualMode.CheckBox"
                                                  FilterType="FilterType.Contains"
                                                  ShowSelectAll="true">
                                        <MultiSelectFieldSettings Text="Name" Value="Id"></MultiSelectFieldSettings>
                                    </SfMultiSelect>
                                </div>
                                <div class="search-item">
                                    <label>Courts</label>
                                    <SfMultiSelect TValue="string[]" TItem="CourtDto"
                                                  Placeholder="Select Courts"
                                                  DataSource="@_courts"
                                                  @bind-Value="_searchCriteria.CourtIds"
                                                  Mode="VisualMode.CheckBox"
                                                  FilterType="FilterType.Contains"
                                                  ShowSelectAll="true">
                                        <MultiSelectFieldSettings Text="Name" Value="Id"></MultiSelectFieldSettings>
                                    </SfMultiSelect>
                                </div>
                            </div>

                            <div class="search-row">
                                <div class="search-item">
                                    <label>Categories</label>
                                    <SfMultiSelect TValue="string[]" TItem="CaseCategoryDto"
                                                  Placeholder="Select Categories"
                                                  DataSource="@_categories"
                                                  @bind-Value="_searchCriteria.CategoryIds"
                                                  Mode="VisualMode.CheckBox"
                                                  FilterType="FilterType.Contains"
                                                  ShowSelectAll="true">
                                        <MultiSelectFieldSettings Text="Title" Value="Id"></MultiSelectFieldSettings>
                                    </SfMultiSelect>
                                </div>
                                <div class="search-item">
                                    <label>Case Natures</label>
                                    <SfMultiSelect TValue="string[]" TItem="CaseNatureDto"
                                                  Placeholder="Select Case Natures"
                                                  DataSource="@_caseNatures"
                                                  @bind-Value="_searchCriteria.CaseNatureIds"
                                                  Mode="VisualMode.CheckBox"
                                                  FilterType="FilterType.Contains"
                                                  ShowSelectAll="true">
                                        <MultiSelectFieldSettings Text="Name" Value="Id"></MultiSelectFieldSettings>
                                    </SfMultiSelect>
                                </div>
                            </div>

                            <div class="search-row">
                                <div class="search-item">
                                    <label>Attachment Types</label>
                                    <SfMultiSelect TValue="string[]" TItem="AttachmentTypeDto"
                                                  Placeholder="Select Attachment Types"
                                                  DataSource="@_attachmentTypes"
                                                  @bind-Value="_searchCriteria.AttachmentTypeIds"
                                                  Mode="VisualMode.CheckBox"
                                                  FilterType="FilterType.Contains"
                                                  ShowSelectAll="true">
                                        <MultiSelectFieldSettings Text="Title" Value="Id"></MultiSelectFieldSettings>
                                    </SfMultiSelect>
                                </div>
                                <div class="search-item">
                                    <label>Decided Status</label>
                                    <SfDropDownList TValue="int" TItem="KeyValuePair<int, string>"
                                                   Placeholder="All Cases"
                                                   DataSource="@_decidedOptions"
                                                   @bind-Value="_decidedFilterValue">
                                        <DropDownListFieldSettings Text="Value" Value="Key"></DropDownListFieldSettings>
                                    </SfDropDownList>
                                </div>
                            </div>

                            <div class="search-row">
                                <div class="search-item">
                                    <label>Date in Office (From)</label>
                                    <SfDatePicker @bind-Value="_searchCriteria.DateFromOffice" Format="dd/MM/yyyy"></SfDatePicker>
                                </div>
                                <div class="search-item">
                                    <label>Date in Office (To)</label>
                                    <SfDatePicker @bind-Value="_searchCriteria.DateToOffice" Format="dd/MM/yyyy"></SfDatePicker>
                                </div>
                            </div>

                            <div class="search-row">
                                <div class="search-item">
                                    <label>Claim Amount (From)</label>
                                    <SfNumericTextBox @bind-Value="_searchCriteria.ClaimAmountFrom" Format="c" Placeholder="0.00"></SfNumericTextBox>
                                </div>
                                <div class="search-item">
                                    <label>Claim Amount (To)</label>
                                    <SfNumericTextBox @bind-Value="_searchCriteria.ClaimAmountTo" Format="c" Placeholder="0.00"></SfNumericTextBox>
                                </div>
                            </div>

                            <div class="search-actions">
                                <SfButton OnClick="PerformSearch" CssClass="e-primary search-btn">
                                    <i class="material-icons">search</i> Search
                                </SfButton>
                                <SfButton OnClick="ClearFilters" CssClass="e-outline">
                                    <i class="material-icons">clear</i> Clear
                                </SfButton>
                            </div>
                        </div>
                    </ContentTemplate>
                </ExpanderItem>
            </ExpanderItems>
        </SfExpander>
    </div>

    <!-- Loading Indicator -->
    @if (_isSearching)
    {
        <div class="search-loading">
            <SfSpinner Visible="_isSearching" Label="Searching..."></SfSpinner>
        </div>
    }

    <!-- Search Results Section -->
    @if (_searchResults != null && !_isSearching)
    {
        <div class="search-results-section">
            <div class="results-summary">
                <h3>Search Results</h3>
                <p>Found @_searchResults.TotalCases cases and @_searchResults.TotalAttachments attachments</p>
            </div>

            <SfTab>
                <TabItems>
                    <TabItem>
                        <ChildContent>
                            <TabHeader Text="@($"Cases ({_searchResults.TotalCases})")"></TabHeader>
                        </ChildContent>
                        <ContentTemplate>
                            <div class="results-grid">
                                <SfGrid DataSource="_searchResults.Cases" AllowSorting="true" AllowFiltering="true" 
                                       AllowPaging="true" Height="500px">
                                    <GridPageSettings PageSize="20"></GridPageSettings>
                                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                                    <GridColumns>
                                        <GridColumn Field="@nameof(CourtCaseSearchResultDto.CaseNumber)" HeaderText="Case #" AutoFit="true"></GridColumn>
                                        <GridColumn Field="@nameof(CourtCaseSearchResultDto.Title)" HeaderText="Title" AutoFit="true"></GridColumn>
                                        <GridColumn Field="@nameof(CourtCaseSearchResultDto.CourtName)" HeaderText="Court" AutoFit="true"></GridColumn>
                                        <GridColumn Field="@nameof(CourtCaseSearchResultDto.CategoryTitle)" HeaderText="Category" AutoFit="true"></GridColumn>
                                        <GridColumn Field="@nameof(CourtCaseSearchResultDto.IsDecidedStr)" HeaderText="Decided" AutoFit="true"></GridColumn>
                                        <GridColumn Field="@nameof(CourtCaseSearchResultDto.PlaintiffCount)" HeaderText="Plaintiffs" AutoFit="true"></GridColumn>
                                        <GridColumn Field="@nameof(CourtCaseSearchResultDto.AttachmentCount)" HeaderText="Attachments" AutoFit="true"></GridColumn>
                                        <GridColumn HeaderText="Actions" AutoFit="true">
                                            <Template>
                                                @{
                                                    var caseResult = (context as CourtCaseSearchResultDto)!;
                                                }
                                                <a href="/court-cases/edit/@caseResult.Id" target="<EMAIL>" class="e-btn e-small e-primary">View</a>
                                            </Template>
                                        </GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            </div>
                        </ContentTemplate>
                    </TabItem>
                    <TabItem>
                        <ChildContent>
                            <TabHeader Text="@($"Attachments ({_searchResults.TotalAttachments})")"></TabHeader>
                        </ChildContent>
                        <ContentTemplate>
                            <div class="results-grid">
                                <SfGrid DataSource="_searchResults.Attachments" AllowSorting="true" AllowFiltering="true" 
                                       AllowPaging="true" Height="500px">
                                    <GridPageSettings PageSize="20"></GridPageSettings>
                                    <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                                    <GridColumns>
                                        <GridColumn Field="@nameof(AttachmentSearchResultDto.FileName)" HeaderText="File Name" AutoFit="true"></GridColumn>
                                        <GridColumn Field="@nameof(AttachmentSearchResultDto.AttachmentTypeName)" HeaderText="Type" AutoFit="true"></GridColumn>
                                        <GridColumn Field="@nameof(AttachmentSearchResultDto.CaseNumber)" HeaderText="Case #" AutoFit="true"></GridColumn>
                                        <GridColumn Field="@nameof(AttachmentSearchResultDto.CaseTitle)" HeaderText="Case Title" AutoFit="true"></GridColumn>
                                        <GridColumn Field="@nameof(AttachmentSearchResultDto.CreatedDate)" HeaderText="Created" AutoFit="true" Format="dd/MM/yyyy"></GridColumn>
                                        <GridColumn Field="@nameof(AttachmentSearchResultDto.FileSizeFormatted)" HeaderText="Size" AutoFit="true"></GridColumn>
                                        <GridColumn HeaderText="Actions" AutoFit="true">
                                            <Template>
                                                @{
                                                    var attachment = (context as AttachmentSearchResultDto)!;
                                                }
                                                <SfButton OnClick="() => ViewCase(attachment.CourtCaseId)" CssClass="e-small e-info">View Case</SfButton>
                                                <SfButton OnClick="() => DownloadFile(attachment.FileUrl)" CssClass="e-small e-success" style="margin-left: 5px;">Download</SfButton>
                                            </Template>
                                        </GridColumn>
                                    </GridColumns>
                                </SfGrid>
                            </div>
                        </ContentTemplate>
                    </TabItem>
                </TabItems>
            </SfTab>
        </div>
    }
</div>

@code {
    private SearchCriteriaDto _searchCriteria = new();
    private SearchResultDto? _searchResults;
    private bool _isSearching = false;

    // Dropdown data sources
    private List<CaseEmployeeDto> _employees = new();
    private List<CourtDto> _courts = new();
    private List<CaseCategoryDto> _categories = new();
    private List<CaseNatureDto> _caseNatures = new();
    private List<AttachmentTypeDto> _attachmentTypes = new();

    private int _decidedFilterValue = 0; // 0 = All, 1 = Yes, 2 = No
    private List<KeyValuePair<int, string>> _decidedOptions = new()
    {
        new(0, "All Cases"),
        new(1, "Decided"),
        new(2, "Non Decided")
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadDropdownData();
    }

    private async Task LoadDropdownData()
    {
        _employees = await EmployeeService.GetCaseEmployeesAsync();
        _courts = await CourtService.GetCourtsAsync();
        _categories = await CaseService.GetCategoriesAsync();
        _caseNatures = await CaseNatureService.GetCaseNaturesAsync();
        _attachmentTypes = await AttachmentTypeService.GetAttachmentTypesAsync();
    }

    private async Task PerformSearch()
    {
        _isSearching = true;

        // Convert dropdown value to bool?
        _searchCriteria.IsDecided = _decidedFilterValue switch
        {
            1 => true,   // Yes
            2 => false,  // No
            _ => null    // All (no filter)
        };

        try
        {
            _searchResults = await SearchService.SearchAsync(_searchCriteria);
        }
        catch (Exception ex)
        {
            // Handle error - you might want to show a toast notification
            Console.WriteLine($"Search error: {ex.Message}");
        }
        finally
        {
            _isSearching = false;
        }
    }

    private void ClearFilters()
    {
        _searchCriteria = new SearchCriteriaDto
        {
            SearchInCases = true,
            SearchInAttachments = true,
            SearchInCaseContent = true,
            SearchInAttachmentNames = true
        };
        _decidedFilterValue = 0;
        _searchResults = null;
    }

    private void ViewCase(int caseId)
    {
        NavMgr.NavigateTo($"/court-cases/edit/{caseId}");
    }

    private void DownloadFile(string fileUrl)
    {
        NavMgr.NavigateTo(fileUrl, true);
    }
}
