using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class SearchService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public async Task<SearchResultDto> SearchAsync(SearchCriteriaDto criteria)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        
        var result = new SearchResultDto();
        
        if (criteria.SearchInCases)
        {
            result.Cases = await SearchCasesAsync(dc, criteria);
            result.TotalCases = result.Cases.Count;
        }
        
        if (criteria.SearchInAttachments)
        {
            result.Attachments = await SearchAttachmentsAsync(dc, criteria);
            result.TotalAttachments = result.Attachments.Count;
        }
        
        return result;
    }
    
    private async Task<List<CourtCaseSearchResultDto>> SearchCasesAsync(ApplicationDbContext dc, SearchCriteriaDto criteria)
    {
        var query = dc.CourtCases
            .AsNoTracking()
            .Where(c => !c.IsDeleted);
        
        // Apply text search
        if (!string.IsNullOrWhiteSpace(criteria.SearchText) && criteria.SearchInCaseContent)
        {
            var searchTerm = criteria.SearchText.ToLower();
            query = query.Where(c => 
                c.Title.ToLower().Contains(searchTerm) ||
                c.CaseNumber.ToLower().Contains(searchTerm) ||
                (c.CaseSynopsis != null && c.CaseSynopsis.ToLower().Contains(searchTerm)) ||
                (c.Pray != null && c.Pray.ToLower().Contains(searchTerm))
            );
        }
        
        // Apply employee filter
        if (criteria.EmployeeIds?.Length > 0)
        {
            query = query.Where(c => c.Plaintiffs.Any(p => criteria.EmployeeIds.Contains(p.CaseEmployeeId.ToString())));
        }
        
        // Apply court filter
        if (criteria.CourtIds?.Length > 0)
        {
            query = query.Where(c => criteria.CourtIds.Contains(c.CourtId.ToString()));
        }
        
        // Apply category filter
        if (criteria.CategoryIds?.Length > 0)
        {
            query = query.Where(c => criteria.CategoryIds.Contains(c.CaseCategoryId.ToString()));
        }
        
        // Apply case nature filter
        if (criteria.CaseNatureIds?.Length > 0)
        {
            query = query.Where(c => c.CaseNatureId.HasValue && criteria.CaseNatureIds.Contains(c.CaseNatureId.Value.ToString()));
        }
        
        // Apply decided filter
        if (criteria.IsDecided.HasValue)
        {
            query = query.Where(c => c.IsDecided == criteria.IsDecided.Value);
        }
        
        // Apply date range filter
        if (criteria.DateFromOffice.HasValue)
        {
            query = query.Where(c => c.DateInOffice >= criteria.DateFromOffice.Value);
        }
        
        if (criteria.DateToOffice.HasValue)
        {
            query = query.Where(c => c.DateInOffice <= criteria.DateToOffice.Value);
        }
        
        // Apply filing year filter
        if (criteria.FilingYearFrom.HasValue)
        {
            query = query.Where(c => c.CaseFilingYear >= criteria.FilingYearFrom.Value.Year);
        }
        
        if (criteria.FilingYearTo.HasValue)
        {
            query = query.Where(c => c.CaseFilingYear <= criteria.FilingYearTo.Value.Year);
        }
        
        // Apply claim amount filter
        if (criteria.ClaimAmountFrom.HasValue)
        {
            query = query.Where(c => c.ClaimAmount >= criteria.ClaimAmountFrom.Value);
        }
        
        if (criteria.ClaimAmountTo.HasValue)
        {
            query = query.Where(c => c.ClaimAmount <= criteria.ClaimAmountTo.Value);
        }
        
        // Include related data and project to DTO
        return await query
            .Include(c => c.Court)
            .Include(c => c.CaseCategory)
            .Include(c => c.CaseNature)
            .Include(c => c.Plaintiffs)
            .Include(c => c.Respondents)
            .Include(c => c.Attachments)
            .Select(c => new CourtCaseSearchResultDto
            {
                Id = c.Id,
                CaseNumber = c.CaseNumber,
                Title = c.Title,
                CourtName = c.Court.Name,
                CaseFilingYear = c.CaseFilingYear,
                CategoryTitle = c.CaseCategory.Title,
                CaseNature = c.CaseNature != null ? c.CaseNature.Name : "",
                IsDecided = c.IsDecided,
                DateInOffice = c.DateInOffice,
                ClaimAmount = c.ClaimAmount,
                PlaintiffCount = c.Plaintiffs.Count(),
                RespondentCount = c.Respondents.Count(r => !r.IsDeleted),
                AttachmentCount = c.Attachments.Count(),
                CaseSynopsis = c.CaseSynopsis,
                Pray = c.Pray
            })
            .OrderByDescending(c => c.DateInOffice)
            .Take(1000) // Limit results for performance
            .ToListAsync();
    }
    
    private async Task<List<AttachmentSearchResultDto>> SearchAttachmentsAsync(ApplicationDbContext dc, SearchCriteriaDto criteria)
    {
        var query = dc.Attachments.AsNoTracking();
        
        // Apply text search in attachment names
        if (!string.IsNullOrWhiteSpace(criteria.SearchText) && criteria.SearchInAttachmentNames)
        {
            var searchTerm = criteria.SearchText.ToLower();
            query = query.Where(a => a.FileName.ToLower().Contains(searchTerm));
        }
        
        // Apply attachment type filter
        if (criteria.AttachmentTypeIds?.Length > 0)
        {
            var attachmentTypeIds = criteria.AttachmentTypeIds.Select(int.Parse).ToList();
            query = query.Where(a => a.AttachmentTypeId.HasValue && attachmentTypeIds.Contains(a.AttachmentTypeId.Value));
        }
        
        // Apply case filters through court case relationship
        if (criteria.EmployeeIds?.Length > 0 || criteria.CourtIds?.Length > 0 || 
            criteria.CategoryIds?.Length > 0 || criteria.CaseNatureIds?.Length > 0 ||
            criteria.IsDecided.HasValue || criteria.DateFromOffice.HasValue || 
            criteria.DateToOffice.HasValue)
        {
            query = query.Where(a => 
                (criteria.EmployeeIds == null || criteria.EmployeeIds.Length == 0 || 
                 a.CourtCase.Plaintiffs.Any(p => criteria.EmployeeIds.Contains(p.CaseEmployeeId.ToString()))) &&
                (criteria.CourtIds == null || criteria.CourtIds.Length == 0 || 
                 criteria.CourtIds.Contains(a.CourtCase.CourtId.ToString())) &&
                (criteria.CategoryIds == null || criteria.CategoryIds.Length == 0 || 
                 criteria.CategoryIds.Contains(a.CourtCase.CaseCategoryId.ToString())) &&
                (criteria.CaseNatureIds == null || criteria.CaseNatureIds.Length == 0 || 
                 (a.CourtCase.CaseNatureId.HasValue && criteria.CaseNatureIds.Contains(a.CourtCase.CaseNatureId.Value.ToString()))) &&
                (!criteria.IsDecided.HasValue || a.CourtCase.IsDecided == criteria.IsDecided.Value) &&
                (!criteria.DateFromOffice.HasValue || a.CourtCase.DateInOffice >= criteria.DateFromOffice.Value) &&
                (!criteria.DateToOffice.HasValue || a.CourtCase.DateInOffice <= criteria.DateToOffice.Value)
            );
        }
        
        // Include related data and project to DTO
        return await query
            .Include(a => a.CourtCase)
            .Include(a => a.AttachmentType)
            .Select(a => new AttachmentSearchResultDto
            {
                Id = a.Id,
                CourtCaseId = a.CourtCaseId,
                CaseNumber = a.CourtCase.CaseNumber,
                CaseTitle = a.CourtCase.Title,
                FileName = a.FileName,
                FileType = a.FileType,
                AttachmentTypeName = a.AttachmentType.Title,
                CreatedDate = a.CreatedDate,
                CreatedBy = a.CreatedBy,
                FileUrl = a.FileUrl,
                FileSizeBytes = 0 // You may need to calculate this from file system
            })
            .OrderByDescending(a => a.CreatedDate)
            .Take(1000) // Limit results for performance
            .ToListAsync();
    }
}
