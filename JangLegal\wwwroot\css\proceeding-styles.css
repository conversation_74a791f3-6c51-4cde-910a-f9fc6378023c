/* Proceeding Management Styles */
.proceedings-timeline {
    margin-top: 1rem;
}

.proceeding-accordion-header-enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.proceeding-main-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.proceeding-date-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.proceeding-date-badge .material-icons {
    font-size: 18px;
}

.date-text {
    font-size: 0.9rem;
}

.proceeding-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.proceeding-type-badge {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.proceeding-status-indicator {
    background: #ffc107;
    color: #212529;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.proceeding-quick-info {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.quick-info-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6c757d;
    font-size: 0.85rem;
}

.quick-info-item .material-icons {
    font-size: 16px;
}

.proceeding-actions-enhanced {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    min-width: auto !important;
    padding: 0.5rem !important;
    border-radius: 50% !important;
}

.action-btn .material-icons {
    font-size: 16px;
}

.proceeding-details-enhanced {
    padding: 1.5rem;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
}

.proceeding-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.info-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid #007bff;
}

.info-card.primary-info {
    border-left-color: #007bff;
}

.info-card.court-info {
    border-left-color: #28a745;
}

.info-card.schedule-info {
    border-left-color: #ffc107;
}

.info-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #495057;
}

.info-title {
    font-weight: 600;
    font-size: 1rem;
}

.info-header .material-icons {
    font-size: 20px;
}

.info-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.info-value {
    font-weight: 400;
    color: #212529;
    text-align: right;
}

.info-value.highlight {
    color: #007bff;
    font-weight: 600;
}

.info-value.status-badge {
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.proceeding-management-section {
    margin-top: 1.5rem;
}

.management-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.management-tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: #e9ecef;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.management-tab-btn:hover {
    background: #dee2e6;
}

.management-tab-btn.active {
    background: #007bff;
    color: white;
}

.management-tab-btn .material-icons {
    font-size: 18px;
}

.remarks-section {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #6c757d;
}

.remarks-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #495057;
    font-weight: 600;
}

.remarks-title .material-icons {
    font-size: 18px;
}

.remarks-content {
    color: #6c757d;
    line-height: 1.5;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .proceeding-accordion-header-enhanced {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .proceeding-quick-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .proceeding-info-grid {
        grid-template-columns: 1fr;
    }
    
    .management-tabs {
        flex-direction: column;
    }
    
    .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .info-value {
        text-align: left;
    }
}
