namespace JangLegal.DTO;

public class SearchResultDto
{
    public List<CourtCaseSearchResultDto> Cases { get; set; } = new();
    public List<AttachmentSearchResultDto> Attachments { get; set; } = new();
    public int TotalCases { get; set; }
    public int TotalAttachments { get; set; }
}

public class CourtCaseSearchResultDto
{
    public int Id { get; set; }
    public string CaseNumber { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string CourtName { get; set; } = string.Empty;
    public int? CaseFilingYear { get; set; }
    public string CategoryTitle { get; set; } = string.Empty;
    public string CaseNature { get; set; } = string.Empty;
    public bool IsDecided { get; set; }
    public string IsDecidedStr => IsDecided ? "Yes" : "No";
    public DateTime? DateInOffice { get; set; }
    public decimal? ClaimAmount { get; set; }
    public int PlaintiffCount { get; set; }
    public int RespondentCount { get; set; }
    public int AttachmentCount { get; set; }
    public string? CaseSynopsis { get; set; }
    public string? Pray { get; set; }
}

public class AttachmentSearchResultDto
{
    public Guid Id { get; set; }
    public int CourtCaseId { get; set; }
    public string CaseNumber { get; set; } = string.Empty;
    public string CaseTitle { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string FileType { get; set; } = string.Empty;
    public string AttachmentTypeName { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string FileUrl { get; set; } = string.Empty;
    public long FileSizeBytes { get; set; }
    public string FileSizeFormatted => FormatFileSize(FileSizeBytes);
    
    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}

public class SearchCriteriaDto
{
    public string? SearchText { get; set; }
    public string[]? EmployeeIds { get; set; }
    public string[]? CourtIds { get; set; }
    public string[]? CategoryIds { get; set; }
    public string[]? CaseNatureIds { get; set; }
    public string[]? AttachmentTypeIds { get; set; }
    public bool? IsDecided { get; set; }
    public DateTime? DateFromOffice { get; set; }
    public DateTime? DateToOffice { get; set; }
    public DateTime? FilingYearFrom { get; set; }
    public DateTime? FilingYearTo { get; set; }
    public decimal? ClaimAmountFrom { get; set; }
    public decimal? ClaimAmountTo { get; set; }
    public bool SearchInCases { get; set; } = true;
    public bool SearchInAttachments { get; set; } = true;
    public bool SearchInCaseContent { get; set; } = true; // Search in synopsis, pray, title
    public bool SearchInAttachmentNames { get; set; } = true;
}
