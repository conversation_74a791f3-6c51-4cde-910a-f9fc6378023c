using JangLegal.DTO;
using JangLegal.Models;
using Microsoft.EntityFrameworkCore;

namespace JangLegal.Services;

public class AttachmentTypeService(IDbContextFactory<ApplicationDbContext> dbContextFactory)
{
    public async Task<List<AttachmentTypeDto>> GetAttachmentTypesAsync()
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        return await dc.AttachmentTypes
            .Select(at => new AttachmentTypeDto
            {
                Id = at.Id,
                Title = at.Title
            })
            .OrderBy(at => at.Title)
            .ToListAsync();
    }

    public async Task<AttachmentTypeDto?> GetAttachmentTypeByIdAsync(int id)
    {
        await using var dc = await dbContextFactory.CreateDbContextAsync();
        var attachmentType = await dc.AttachmentTypes.FindAsync(id);
        
        if (attachmentType == null) return null;
        
        return new AttachmentTypeDto
        {
            Id = attachmentType.Id,
            Title = attachmentType.Title
        };
    }

    public async Task<string> SaveAttachmentTypeAsync(AttachmentTypeDto dto, string userId)
    {
        try
        {
            await using var dc = await dbContextFactory.CreateDbContextAsync();

            if (dto.Id == 0)
            {
                // Check for duplicate title
                if (await dc.AttachmentTypes.AnyAsync(at => at.Title.ToLower() == dto.Title.ToLower()))
                {
                    return "An attachment type with this title already exists";
                }

                var attachmentType = new AttachmentType
                {
                    Title = dto.Title.Trim()
                };

                dc.AttachmentTypes.Add(attachmentType);
                await dc.SaveChangesAsync();
                return "OK";
            }
            else
            {
                // Update existing attachment type
                var attachmentType = await dc.AttachmentTypes.FindAsync(dto.Id);
                if (attachmentType == null)
                {
                    return "Attachment type not found";
                }

                // Check for duplicate title (excluding current record)
                if (await dc.AttachmentTypes.AnyAsync(at => at.Id != dto.Id && at.Title.ToLower() == dto.Title.ToLower()))
                {
                    return "An attachment type with this title already exists";
                }

                attachmentType.Title = dto.Title.Trim();
                await dc.SaveChangesAsync();
                return "OK";
            }
        }
        catch (Exception ex)
        {
            return $"Error saving attachment type: {ex.Message}";
        }
    }

    public async Task<string> DeleteAttachmentTypeAsync(int id, string userId)
    {
        try
        {
            await using var dc = await dbContextFactory.CreateDbContextAsync();
            var attachmentType = await dc.AttachmentTypes.FindAsync(id);
            
            if (attachmentType == null)
            {
                return "Attachment type not found";
            }

            // Check if attachment type is being used
            var isUsed = await dc.Attachments.AnyAsync(a => a.AttachmentTypeId == id);
            if (isUsed)
            {
                return "Cannot delete attachment type as it is being used by existing attachments";
            }

            dc.AttachmentTypes.Remove(attachmentType);
            await dc.SaveChangesAsync();
            return "OK";
        }
        catch (Exception ex)
        {
            return $"Error deleting attachment type: {ex.Message}";
        }
    }
}
