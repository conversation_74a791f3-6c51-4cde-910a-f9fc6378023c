@using JangLegal.DTO

<!-- Verdict Selector Dialog -->
<SfDialog @bind-Visible="IsVisible" Width="800px" IsModal="true">
    <DialogTemplates>
        <Header>Select Verdict</Header>
        <Content>
            <SfGrid DataSource="Verdicts" AllowPaging="true">
                <GridColumns>
                    <GridColumn Field="@nameof(CaseVerdictDto.Title)" HeaderText="Title"></GridColumn>
                    <GridColumn Field="@nameof(CaseVerdictDto.Description)" HeaderText="Description"></GridColumn>
                    <GridColumn HeaderText="Actions" Width="100">
                        <Template>
                            @{
                                if (context is CaseVerdictDto verdict)
                                {
                                    <SfButton OnClick="@(() => SelectVerdict(verdict))"
                                              type="button"
                                              CssClass="e-primary">
                                        Select
                                    </SfButton>
                                }
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
    [Parameter] public List<CaseVerdictDto> Verdicts { get; set; } = new();
    [Parameter] public EventCallback<CaseVerdictDto> OnVerdictSelected { get; set; }

    private async Task SelectVerdict(CaseVerdictDto verdict)
    {
        await OnVerdictSelected.InvokeAsync(verdict);
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(IsVisible);
    }
}
