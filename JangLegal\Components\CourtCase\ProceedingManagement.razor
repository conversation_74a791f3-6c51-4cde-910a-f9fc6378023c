@using JangLegal.DTO

<!-- Case Proceedings Section - Only visible when editing -->
@if (CaseId.HasValue)
{
    <div class="card mb-3">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>Case Proceedings</h5>
                <SfButton type="button" OnClick="OpenProceedingDialog" CssClass="e-primary">Add Proceeding</SfButton>
            </div>

            @if (CaseDto.CaseProceedings.Any())
            {
                <div class="proceedings-timeline">
                    <SfAccordion>
                        <AccordionItems>
                            @foreach (var proceeding in CaseDto.CaseProceedings.OrderBy(p => p.ProceedingDate))
                            {
                                <AccordionItem>
                                    <HeaderTemplate>
                                        <div class="proceeding-accordion-header-enhanced">
                                            <div class="proceeding-main-info">
                                                <div class="proceeding-date-badge">
                                                    <i class="material-icons">event</i>
                                                    <span class="date-text">@proceeding.ProceedingDateFormatted</span>
                                                </div>
                                                <div class="proceeding-meta">
                                                    <span class="proceeding-type-badge">@proceeding.ProceedingTypeName</span>
                                                    <span class="proceeding-status-indicator">@proceeding.ProceedingStatusName</span>
                                                </div>
                                            </div>
                                            <div class="proceeding-quick-info">
                                                @if (!string.IsNullOrEmpty(proceeding.PresidingJudge))
                                                {
                                                    <div class="quick-info-item">
                                                        <i class="material-icons">gavel</i>
                                                        <span>@proceeding.PresidingJudge</span>
                                                    </div>
                                                }
                                                @if (!string.IsNullOrEmpty(proceeding.CourtRoomNumber))
                                                {
                                                    <div class="quick-info-item">
                                                        <i class="material-icons">meeting_room</i>
                                                        <span>Room @proceeding.CourtRoomNumber</span>
                                                    </div>
                                                }
                                            </div>
                                            <div class="proceeding-actions-enhanced">
                                                <SfButton OnClick="@(() => EditProceeding(proceeding))"
                                                          type="button"
                                                          CssClass="e-info e-small action-btn">
                                                    <i class="material-icons">edit</i>
                                                </SfButton>
                                                <SfButton OnClick="@(() => DeleteProceeding(proceeding.ProceedingId))"
                                                          type="button"
                                                          CssClass="e-danger e-small action-btn">
                                                    <i class="material-icons">delete</i>
                                                </SfButton>
                                            </div>
                                        </div>
                                    </HeaderTemplate>
                                    <ContentTemplate>
                                        <div class="proceeding-details-enhanced">
                                            <!-- Key Information Cards -->
                                            <div class="proceeding-info-grid">
                                                <div class="info-card primary-info">
                                                    <div class="info-header">
                                                        <i class="material-icons">gavel</i>
                                                        <span class="info-title">Proceeding Details</span>
                                                    </div>
                                                    <div class="info-content">
                                                        <div class="info-row">
                                                            <span class="info-label">Type</span>
                                                            <span class="info-value highlight">@proceeding.ProceedingTypeName</span>
                                                        </div>
                                                        <div class="info-row">
                                                            <span class="info-label">Status</span>
                                                            <span class="info-value status-badge">@proceeding.ProceedingStatusName</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="info-card court-info">
                                                    <div class="info-header">
                                                        <i class="material-icons">account_balance</i>
                                                        <span class="info-title">Court Information</span>
                                                    </div>
                                                    <div class="info-content">
                                                        <div class="info-row">
                                                            <span class="info-label">Presiding Judge</span>
                                                            <span class="info-value">@proceeding.PresidingJudge</span>
                                                        </div>
                                                        <div class="info-row">
                                                            <span class="info-label">Court Room</span>
                                                            <span class="info-value">@proceeding.CourtRoomNumber</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                @if (proceeding.NextProceedingDate.HasValue)
                                                {
                                                    <div class="info-card schedule-info">
                                                        <div class="info-header">
                                                            <i class="material-icons">schedule</i>
                                                            <span class="info-title">Next Proceeding</span>
                                                        </div>
                                                        <div class="info-content">
                                                            <div class="info-row">
                                                                <span class="info-label">Scheduled Date</span>
                                                                <span class="info-value highlight">@proceeding.NextProceedingDate?.ToString("dd MMM, yyyy")</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>

                                            <!-- Management Section -->
                                            <div class="proceeding-management-section">
                                                <div class="management-tabs">
                                                    <button class="management-tab-btn" @onclick="@(() => ManageOutcomes(proceeding.ProceedingId))">
                                                        <i class="material-icons">assignment_turned_in</i>
                                                        <span>Outcomes (@(proceeding.ProceedingOutcomes?.Count ?? 0))</span>
                                                    </button>
                                                    <button class="management-tab-btn" @onclick="@(() => ManageAttendees(proceeding.ProceedingId))">
                                                        <i class="material-icons">people</i>
                                                        <span>Attendees (@(proceeding.ProceedingAttendees?.Count ?? 0))</span>
                                                    </button>
                                                    <button class="management-tab-btn" @onclick="@(() => ManageDocuments(proceeding.ProceedingId))">
                                                        <i class="material-icons">description</i>
                                                        <span>Documents (@(proceeding.ProceedingDocuments?.Count ?? 0))</span>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Remarks Section -->
                                            @if (!string.IsNullOrEmpty(proceeding.Remarks))
                                            {
                                                <div class="remarks-section">
                                                    <div class="remarks-title">
                                                        <i class="material-icons">note</i>
                                                        <span>Remarks</span>
                                                    </div>
                                                    <div class="remarks-content">
                                                        @proceeding.Remarks
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </ContentTemplate>
                                </AccordionItem>
                            }
                        </AccordionItems>
                    </SfAccordion>
                </div>
            }
            else
            {
                <p class="text-muted">No proceedings recorded yet.</p>
            }
        </div>
    </div>
}

@code {
    [Parameter] public CourtCaseDto CaseDto { get; set; } = new();
    [Parameter] public int? CaseId { get; set; }
    [Parameter] public EventCallback OnOpenProceedingDialog { get; set; }
    [Parameter] public EventCallback<CaseProceedingDto> OnEditProceeding { get; set; }
    [Parameter] public EventCallback<int> OnDeleteProceeding { get; set; }
    [Parameter] public EventCallback<int> OnManageOutcomes { get; set; }
    [Parameter] public EventCallback<int> OnManageAttendees { get; set; }
    [Parameter] public EventCallback<int> OnManageDocuments { get; set; }

    private async Task OpenProceedingDialog()
    {
        await OnOpenProceedingDialog.InvokeAsync();
    }

    private async Task EditProceeding(CaseProceedingDto proceeding)
    {
        await OnEditProceeding.InvokeAsync(proceeding);
    }

    private async Task DeleteProceeding(int proceedingId)
    {
        await OnDeleteProceeding.InvokeAsync(proceedingId);
    }

    private async Task ManageOutcomes(int proceedingId)
    {
        await OnManageOutcomes.InvokeAsync(proceedingId);
    }

    private async Task ManageAttendees(int proceedingId)
    {
        await OnManageAttendees.InvokeAsync(proceedingId);
    }

    private async Task ManageDocuments(int proceedingId)
    {
        await OnManageDocuments.InvokeAsync(proceedingId);
    }
}
