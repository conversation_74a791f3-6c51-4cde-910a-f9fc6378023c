@using FilterType = Syncfusion.Blazor.DropDowns.FilterType
@using JangLegal.DTO

<div class="card mb-3">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <SfTextBox @bind-Value="CaseDto.CaseNumber"
                           Placeholder="Case Number"
                           FloatLabelType="FloatLabelType.Always">
                </SfTextBox>
                <ValidationMessage For="@(() => CaseDto.CaseNumber)"/>
            </div>

            <div class="col-md-8">
                <SfTextBox @bind-Value="CaseDto.Title"
                           Placeholder="Case Title"
                           FloatLabelType="FloatLabelType.Always">
                </SfTextBox>
            </div>

            <div class="col-md-4">
                <SfDropDownList TValue="int" TItem="CourtDto"
                                @bind-Value="CaseDto.CourtId"
                                DataSource="@Courts"
                                AllowFiltering="true"
                                ShowClearButton="true"
                                FilterType="FilterType.Contains"
                                Placeholder="Select Court"
                                FloatLabelType="FloatLabelType.Always">
                    <DropDownListFieldSettings Text="Name" Value="Id"/>
                </SfDropDownList>
                <ValidationMessage For="@(() => CaseDto.CourtId)"/>
            </div>

            <div class="col-md-4">
                <SfNumericTextBox TValue="int?" @bind-Value="CaseDto.CaseFilingYear"
                                  Placeholder="Filing Year"
                                  FloatLabelType="FloatLabelType.Always">
                </SfNumericTextBox>
            </div>

            <div class="col-md-4">
                <SfDropDownList TValue="int?" TItem="CaseCategoryDto"
                                @bind-Value="CaseDto.CaseCategoryId"
                                DataSource="@Categories"
                                Placeholder="Select Category"
                                AllowFiltering="true"
                                ShowClearButton="true"
                                FilterType="FilterType.Contains"
                                FloatLabelType="FloatLabelType.Always">
                    <DropDownListFieldSettings Text="Title" Value="Id"/>
                </SfDropDownList>
                <ValidationMessage For="@(() => CaseDto.CaseCategoryId)"/>
            </div>

            <div class="col-md-4">
                <SfDatePicker TValue="DateTime?" @bind-Value="CaseDto.DateInOffice"
                              Placeholder="Date in Office"
                              FloatLabelType="FloatLabelType.Always">
                </SfDatePicker>
            </div>

            <div class="col-md-4">
                <SfNumericTextBox TValue="decimal?" @bind-Value="CaseDto.ClaimAmount"
                                  Format="c2"
                                  Placeholder="Claim Amount"
                                  FloatLabelType="FloatLabelType.Always">
                </SfNumericTextBox>
            </div>

            <div class="col-md-4">
                <SfDropDownList TValue="int?" TItem="CaseNatureDto"
                                @bind-Value="CaseDto.CaseNatureId"
                                DataSource="@CaseNatures"
                                Placeholder="Select Case Nature"
                                FloatLabelType="FloatLabelType.Always"
                                AllowFiltering="true"
                                ShowClearButton="true"
                                FilterType="FilterType.Contains">
                    <DropDownListFieldSettings Text="Name" Value="Id"/>
                </SfDropDownList>
            </div>
            
            <div class="col-md-4">
                <div>Decided</div>
                <SfSwitch OnLabel="Yes" OffLabel="No" @bind-Checked="CaseDto.IsDecided"></SfSwitch>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public CourtCaseDto CaseDto { get; set; } = new();
    [Parameter] public List<CourtDto> Courts { get; set; } = new();
    [Parameter] public List<CaseCategoryDto> Categories { get; set; } = new();
    [Parameter] public List<CaseNatureDto> CaseNatures { get; set; } = new();
}
