@import '/_content/Microsoft.FluentUI.AspNetCore.Components/css/reboot.css';

body {
    --body-font: "Segoe UI Variable", "Segoe UI", sans-serif;
    font-family: var(--body-font);
    font-size: var(--type-ramp-base-font-size);
    line-height: var(--type-ramp-base-line-height);
    margin: 0;
}

.navmenu-icon {
    display: none;
}

.main {
    min-height: calc(100dvh - 86px);
    color: var(--neutral-foreground-rest);
    align-items: stretch !important;
}

.body-content {
    align-self: stretch;
    height: unset !important;
    display: flex;
}

.content {
    padding: 0.5rem 1.5rem;
    align-self: stretch !important;
    width: 100%;
}

.manage {
    width: 100dvw;
}

footer {
    display: grid;
    grid-template-columns: 10px auto auto 10px;
    background: var(--neutral-layer-4);
    color: var(--neutral-foreground-rest);
    align-items: center;
    padding: 10px 10px;
}

    footer .link1 {
        grid-column: 2;
        justify-content: start;
    }

    footer .link2 {
        grid-column: 3;
        justify-self: end;
    }

    footer a {
        color: var(--neutral-foreground-rest);
        text-decoration: none;
    }

        footer a:focus {
            outline: 1px dashed;
            outline-offset: 3px;
        }

        footer a:hover {
            text-decoration: underline;
        }

.alert {
    border: 1px dashed var(--accent-fill-rest);
    padding: 5px;
}


#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
    margin: 20px 0;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::before {
        content: "An error has occurred. "
    }

.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

    .loading-progress circle {
        fill: none;
        stroke: #e0e0e0;
        stroke-width: 0.6rem;
        transform-origin: 50% 50%;
        transform: rotate(-90deg);
    }

        .loading-progress circle:last-child {
            stroke: #1b6ec2;
            stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
            transition: stroke-dasharray 0.05s ease-in-out;
        }

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

    .loading-progress-text:after {
        content: var(--blazor-load-percentage-text, "Loading");
    }

code {
    color: #c02d76;
}

@media (max-width: 600px) {
    .main {
        flex-direction: column !important;
        row-gap: 0 !important;
    }

    nav.sitenav {
        width: 100%;
        height: 100%;
    }

    #main-menu {
        width: 100% !important;
    }

        #main-menu > div:first-child:is(.expander) {
            display: none;
        }

    .navmenu {
        width: 100%;
    }

    #navmenu-toggle {
        appearance: none;
    }

        #navmenu-toggle ~ nav {
            display: none;
        }

        #navmenu-toggle:checked ~ nav {
            display: block;
        }

    .navmenu-icon {
        cursor: pointer;
        z-index: 10;
        display: block;
        position: absolute;
        top: 15px;
        right: 20px;
        width: 20px;
        height: 20px;
        border: none;
    }
}


.loginForm{
    width: 500px;
    margin: auto auto;
    margin-top: 10vh;
    background: #eee;
    padding: 20px;
    border-radius: 12px;
              border: 1px solid gray;
              box-shadow: 2px 2px 5px rgba(0,0,0,0.3);
          }

.loginForm input {
  width: 100%;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
  border: 1px solid #ccc;
  font-size: 16px;
}

.loginForm .loginBtn {
  background-color: #4caf50;
  border: none;
  color: white;
  padding: 15px 32px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 8px;
}


@media (max-width: 600px) {
    .loginForm {
      width: 90%;
      margin: 20px auto;
      padding: 10px;
    }
  
    .loginForm input {
      padding: 10px;
      font-size: 18px;
    }
  
    .loginForm .loginBtn {
      padding: 10px 20px;
      font-size: 18px;
    }
  }

/* Card Styles */
.card {
    background: var(--neutral-layer-1);
    border-radius: 8px;
    border: 1px solid var(--neutral-stroke-rest);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.2s ease-in-out;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 1.25rem;
}

.card-header {
    padding: 1rem 1.25rem;
    background: var(--neutral-layer-2);
    border-bottom: 1px solid var(--neutral-stroke-rest);
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-footer {
    padding: 1rem 1.25rem;
    background: var(--neutral-layer-2);
    border-top: 1px solid var(--neutral-stroke-rest);
    border-radius: 0 0 8px 8px;
}

/* Card Title Styles */
.card-title {
    margin: 0;
    font-size: var(--type-ramp-plus-1-font-size);
    line-height: var(--type-ramp-plus-1-line-height);
    font-weight: 600;
}

.card-subtitle {
    margin: 0.25rem 0 0;
    font-size: var(--type-ramp-base-font-size);
    color: var(--neutral-foreground-hint);
}

/* Card Content Styles */
.card-text {
    margin-bottom: 1rem;
    color: var(--neutral-foreground-rest);
}

.card-text:last-child {
    margin-bottom: 0;
}

/* Card Groups */
.card-group {
    display: grid;
    gap: 1rem;
    margin: 1rem 0;
}

@media (min-width: 768px) {
    .card-group {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}
  
.gap-2
{
    gap: 0.5rem;
}

.user-welcome {
  display: inline-block;
}

@media (max-width: 600px) {
  .user-welcome {
    display: none;
  }
}

/* Proceeding Styles */
.proceedings-timeline {
    position: relative;
}

.proceeding-card {
    background: var(--neutral-layer-1);
    border: 1px solid var(--neutral-stroke-rest);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.2s ease-in-out;
    position: relative;
    margin-left: 20px;
}

.proceeding-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.proceeding-card::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 20px;
    width: 12px;
    height: 12px;
    background: var(--accent-fill-rest);
    border-radius: 50%;
    border: 3px solid var(--neutral-layer-1);
}

.proceeding-card:not(:last-child)::after {
    content: '';
    position: absolute;
    left: -15px;
    top: 32px;
    width: 2px;
    height: calc(100% - 12px);
    background: var(--neutral-stroke-rest);
}

.proceeding-header {
    padding: 1rem 1.25rem;
    background: var(--neutral-layer-2);
    border-bottom: 1px solid var(--neutral-stroke-rest);
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.proceeding-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--neutral-foreground-rest);
}

.proceeding-date .material-icons {
    font-size: 18px;
    color: var(--accent-fill-rest);
}

.proceeding-date .time {
    font-weight: 400;
    color: var(--neutral-foreground-hint);
    margin-left: 0.5rem;
}

.proceeding-actions {
    display: flex;
    gap: 0.5rem;
}

/* Enhanced Accordion Header */
.proceeding-accordion-header-enhanced {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 8px;
    margin: 0.25rem 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    gap: 1rem;
}

.proceeding-accordion-header-enhanced:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.proceeding-main-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
}

.proceeding-date-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    width: fit-content;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.proceeding-date-badge .material-icons {
    font-size: 18px;
}

.date-text {
    letter-spacing: 0.5px;
}

.proceeding-meta {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

.proceeding-type-badge {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.proceeding-status-indicator {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.proceeding-quick-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
    max-width: 300px;
}

.quick-info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.85rem;
}

.quick-info-item .material-icons {
    font-size: 16px;
    color: #007bff;
}

.proceeding-actions-enhanced {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.proceeding-actions-enhanced .action-btn {
    border-radius: 50%;
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.proceeding-actions-enhanced .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.proceeding-actions-enhanced .action-btn .material-icons {
    font-size: 16px;
    margin: 0;
}

/* Enhanced Proceeding Details Styles */
.proceeding-details-enhanced {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.proceeding-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.25rem;
    margin-bottom: 1.5rem;
}

.info-card {
    background: #ffffff;
    border-radius: 10px;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3);
}

.info-card.primary-info::before {
    background: linear-gradient(90deg, #007bff, #0056b3);
}

.info-card.court-info::before {
    background: linear-gradient(90deg, #28a745, #1e7e34);
}

.info-card.schedule-info::before {
    background: linear-gradient(90deg, #ffc107, #e0a800);
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.info-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f1f3f4;
}

.info-header .material-icons {
    font-size: 20px;
    margin-right: 0.75rem;
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
    padding: 8px;
    border-radius: 50%;
}

.court-info .info-header .material-icons {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.schedule-info .info-header .material-icons {
    color: #ffc107;
    background: rgba(255, 193, 7, 0.1);
}

.info-title {
    font-weight: 600;
    font-size: 1rem;
    color: #2c3e50;
    letter-spacing: 0.5px;
}

.info-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
    text-align: right;
    max-width: 60%;
    word-wrap: break-word;
}

.info-value.highlight {
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
}

.info-value.status-badge {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.next-date-display {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-radius: 8px;
    border: 1px solid #ffc107;
}

.next-date {
    font-size: 1.1rem;
    font-weight: 700;
    color: #856404;
    display: block;
}

.remarks-section {
    background: #ffffff;
    border-radius: 10px;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.remarks-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6f42c1, #5a32a3);
}

.remarks-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f1f3f4;
}

.remarks-header .material-icons {
    font-size: 20px;
    margin-right: 0.75rem;
    color: #6f42c1;
    background: rgba(111, 66, 193, 0.1);
    padding: 8px;
    border-radius: 50%;
}

.remarks-title {
    font-weight: 600;
    font-size: 1rem;
    color: #2c3e50;
    letter-spacing: 0.5px;
}

.remarks-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #6f42c1;
    white-space: pre-wrap;
    line-height: 1.6;
    color: #495057;
    font-size: 0.95rem;
}

/* Legacy styles for backward compatibility */
.proceeding-details {
    padding: 1.25rem;
}

.detail-item {
    margin-bottom: 0.75rem;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-item label {
    font-weight: 600;
    color: var(--neutral-foreground-hint);
    display: block;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.detail-item span {
    color: var(--neutral-foreground-rest);
}

.detail-item .remarks {
    background: var(--neutral-layer-2);
    padding: 0.75rem;
    border-radius: 4px;
    border: 1px solid var(--neutral-stroke-rest);
    margin-top: 0.25rem;
    white-space: pre-wrap;
}

/* Responsive Design for Enhanced Proceeding Details */
@media (max-width: 768px) {
    .proceeding-details-enhanced {
        padding: 1rem;
        margin: 0 -0.5rem;
    }

    .proceeding-info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .info-card {
        padding: 1rem;
    }

    .info-header .material-icons {
        font-size: 18px;
        padding: 6px;
        margin-right: 0.5rem;
    }

    .info-title {
        font-size: 0.9rem;
    }

    .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        padding: 0.375rem 0;
    }

    .info-value {
        text-align: left;
        max-width: 100%;
        font-size: 0.9rem;
    }

    .info-value.highlight,
    .info-value.status-badge {
        font-size: 0.8rem;
        padding: 0.2rem 0.6rem;
    }

    .next-date-display {
        padding: 0.75rem;
    }

    .next-date {
        font-size: 1rem;
    }

    .remarks-section {
        padding: 1rem;
    }

    .remarks-content {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .proceeding-details-enhanced {
        padding: 0.75rem;
        border-radius: 8px;
    }

    .info-card {
        padding: 0.75rem;
        border-radius: 8px;
    }

    .info-header {
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
    }

    .info-content {
        gap: 0.5rem;
    }

    .remarks-section {
        padding: 0.75rem;
        border-radius: 8px;
    }

    .remarks-header {
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
    }

    /* Enhanced Management Sections Mobile */
    .outcome-management-enhanced,
    .attendee-management-enhanced,
    .document-management-enhanced {
        padding: 1rem;
        margin-top: 1rem;
        border-radius: 8px;
    }

    .management-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
    }

    .header-content {
        justify-content: center;
        gap: 0.5rem;
    }

    .header-content .material-icons {
        font-size: 20px;
        padding: 8px;
    }

    .section-title {
        font-size: 1rem;
    }

    .management-header .e-btn {
        justify-content: center;
        padding: 0.75rem 1rem;
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .outcome-management-enhanced,
    .attendee-management-enhanced,
    .document-management-enhanced {
        padding: 0.75rem;
        margin-top: 0.75rem;
    }

    .management-header {
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
    }

    .header-content .material-icons {
        font-size: 18px;
        padding: 6px;
    }

    .section-title {
        font-size: 0.95rem;
    }

    /* Enhanced Accordion Header Small Mobile */
    .proceeding-accordion-header-enhanced {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .proceeding-date-badge {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    .proceeding-date-badge .material-icons {
        font-size: 16px;
    }

    .proceeding-type-badge,
    .proceeding-status-indicator {
        font-size: 0.75rem;
        padding: 0.2rem 0.6rem;
    }

    .quick-info-item {
        font-size: 0.8rem;
    }

    .quick-info-item .material-icons {
        font-size: 14px;
    }

    .proceeding-actions-enhanced .action-btn {
        width: 32px;
        height: 32px;
    }

    .proceeding-actions-enhanced .action-btn .material-icons {
        font-size: 14px;
    }
}

.proceeding-summary {
    border-top: 1px solid var(--neutral-stroke-rest);
    padding-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.summary-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--neutral-foreground-hint);
}

.stat-item .material-icons {
    font-size: 16px;
    color: var(--accent-fill-rest);
}

.summary-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* File Upload Styles */
.file-upload-container {
    position: relative;
    display: inline-block;
    width: 100%;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px dashed var(--neutral-stroke-rest);
    border-radius: 8px;
    background: var(--neutral-layer-2);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    color: var(--neutral-foreground-hint);
}

.file-upload-label:hover {
    border-color: var(--accent-fill-rest);
    background: var(--neutral-layer-3);
    color: var(--neutral-foreground-rest);
}

.file-upload-label .material-icons {
    font-size: 24px;
    color: var(--accent-fill-rest);
}

.file-preview {
    margin-top: 1rem;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--neutral-layer-2);
    border: 1px solid var(--neutral-stroke-rest);
    border-radius: 6px;
}

.file-info .material-icons {
    font-size: 24px;
    color: var(--accent-fill-rest);
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--neutral-foreground-rest);
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.875rem;
    color: var(--neutral-foreground-hint);
}

.btn-remove {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--neutral-foreground-hint);
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
}

.btn-remove:hover {
    background: var(--neutral-fill-stealth-hover);
    color: var(--error-fill-rest);
}

.btn-remove .material-icons {
    font-size: 18px;
}

.file-requirements {
    margin-top: 0.5rem;
}

.file-requirements .material-icons {
    font-size: 16px;
    vertical-align: middle;
    margin-right: 0.25rem;
}

/* Proceeding Accordion Styles */
.proceeding-accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.5rem 0;
}

.proceeding-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.proceeding-date .material-icons {
    color: var(--accent-fill-rest);
    font-size: 20px;
}

.proceeding-type {
    color: var(--neutral-foreground-hint);
    font-weight: 400;
    margin-left: 0.5rem;
}

.proceeding-actions {
    display: flex;
    gap: 0.5rem;
}

/* Enhanced Management Sections */
.outcome-management-enhanced,
.attendee-management-enhanced,
.document-management-enhanced {
    background: #ffffff;
    border-radius: 10px;
    padding: 1.25rem;
    margin-top: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.outcome-management-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #17a2b8, #138496);
}

.attendee-management-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #fd7e14, #e55a00);
}

.document-management-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #dc3545, #c82333);
}

.management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-content .material-icons {
    font-size: 24px;
    padding: 10px;
    border-radius: 50%;
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.outcome-management-enhanced .header-content .material-icons {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

.attendee-management-enhanced .header-content .material-icons {
    background: rgba(253, 126, 20, 0.1);
    color: #fd7e14;
}

.document-management-enhanced .header-content .material-icons {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.section-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: 0.5px;
}

.management-header .e-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 25px;
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.management-header .e-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.management-header .e-btn .material-icons {
    font-size: 16px;
    margin: 0;
    padding: 0;
    background: none;
    color: inherit;
}

/* Legacy Management Dialog Styles */
.outcome-management,
.attendee-management,
.document-management {
    max-height: 500px;
    overflow-y: auto;
}

.outcome-item,
.attendee-item,
.document-item {
    transition: all 0.2s ease-in-out;
}

.outcome-item:hover,
.attendee-item:hover,
.document-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.outcome-details,
.attendee-details {
    flex: 1;
}

.outcomes-list,
.attendees-list,
.documents-list {
    max-height: 400px;
    overflow-y: auto;
}

.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.bg-success {
    background-color: var(--success-fill-rest) !important;
    color: white;
}

.bg-warning {
    background-color: var(--warning-fill-rest) !important;
    color: white;
}

.btn-group {
    display: flex;
    gap: 0.25rem;
}

.document-item .material-icons {
    font-size: 24px;
}

/* File Input Styling */
.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--neutral-foreground-rest);
    background-color: var(--neutral-layer-1);
    background-image: none;
    border: 1px solid var(--neutral-stroke-rest);
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: var(--neutral-foreground-rest);
    background-color: var(--neutral-layer-1);
    border-color: var(--accent-fill-rest);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(var(--accent-fill-rest), 0.25);
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--neutral-foreground-rest);
}

/* Responsive Design for Proceedings */
@media (max-width: 768px) {
    .proceeding-card {
        margin-left: 0;
    }

    .proceeding-card::before,
    .proceeding-card::after {
        display: none;
    }

    .proceeding-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .proceeding-actions {
        width: 100%;
        justify-content: flex-end;
    }

    /* Enhanced Accordion Header Mobile */
    .proceeding-accordion-header-enhanced {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        padding: 1rem;
    }

    .proceeding-main-info {
        align-items: center;
        text-align: center;
    }

    .proceeding-date-badge {
        align-self: center;
    }

    .proceeding-meta {
        justify-content: center;
    }

    .proceeding-quick-info {
        max-width: none;
        align-items: center;
    }

    .proceeding-actions-enhanced {
        justify-content: center;
    }

    .proceeding-summary {
        flex-direction: column;
        align-items: flex-start;
    }

    .summary-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .outcome-item .row,
    .document-item .row {
        flex-direction: column;
    }

    .outcome-item .col-md-4,
    .document-item .col-md-4 {
        text-align: left !important;
        margin-top: 1rem;
    }
}

.e-accordion .e-acrdn-item .e-acrdn-header .e-acrdn-header-content 
{
    background-color: #b8b8b8;
    padding: 10px;
    border-radius: 5px;
    font-weight: bold;
}

.e-acrdn-header-content {
    width: 100%;
}
.content {
    padding: 10px 15px !important
}


.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */
    z-index: 9999; /* Ensure it's on top */
    display: flex;
    justify-content: center;
    align-items: center;
}

.overlay-content {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    text-align: center;
}

/* Optional: Spinner CSS */
.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #333;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 10px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}